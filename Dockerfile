# Start from the latest golang base image
FROM golang:latest as builder

# Add Maintainer Info
LABEL maintainer="<PERSON><PERSON><PERSON> <<EMAIL>>"

# Set the Current Working Directory inside the container
WORKDIR /app

# install upx
RUN echo 'deb http://ftp.de.debian.org/debian sid main' >> /etc/apt/sources.list && \
    apt-get update && \
    apt-get install -y upx-ucl

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download all dependencies. Dependencies will be cached if the go.mod and go.sum files are not changed
RUN go mod download

# Copy the source from the current directory to the Working Directory inside the container
COPY . .

# Build the Go app
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -o bpcspm . && upx --best --lzma bpcspm

######## Start a new stage from scratch #######
FROM scratch 

# Copy the Pre-built binary file from the previous stage
COPY --from=builder /app/bpcspm .

# Copy the necessary files for ca-certificates and timezone
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /usr/share/zoneinfo/Asia/Jakarta /etc/localtime

# Set the timezone environment variable
ENV TZ=Asia/Jakarta

# Command to run the executable
CMD ["./bpcspm"] 