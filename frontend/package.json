{"name": "dashboard-pm", "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "test:unit": "vitest", "test": "npm run test:unit -- --run && npm run test:e2e", "test:e2e": "playwright test"}, "devDependencies": {"@internationalized/date": "^3.6.0", "@playwright/test": "^1.45.3", "@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-static": "^3.0.6", "@sveltejs/kit": "2.15.2", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@types/dom-to-image": "^2.6.7", "autoprefixer": "^10.4.20", "bits-ui": "^1.0.0-next.72", "clsx": "^2.1.1", "embla-carousel-svelte": "^8.5.1", "eslint": "^9.7.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.36.0", "formsnap": "2.0.0-next.1", "globals": "^15.0.0", "lucide-svelte": "^0.468.0", "mode-watcher": "^0.5.0", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "svelte-sonner": "^0.3.28", "sveltekit-superforms": "^2.20.1", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.3.0", "tailwindcss": "^3.4.9", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "typescript-eslint": "^8.0.0", "vaul-svelte": "1.0.0-next.3", "vite": "^5.0.3", "vitest": "^2.0.4", "zod": "^3.23.8"}, "dependencies": {"@tabler/icons-svelte": "^3.28.1", "@tailwindcss/aspect-ratio": "^0.4.2", "@tanstack/table-core": "^8.20.5", "@unovis/svelte5": "file:/home/<USER>/Projects/js/likearthian/unovis/packages/svelte5", "@unovis/ts": "file:/home/<USER>/Projects/js/likearthian/unovis/packages/ts/lib", "dom-to-image": "^2.6.0", "embla-carousel-autoplay": "^8.5.1", "html-to-image": "^1.11.11", "mini-svg-data-uri": "^1.4.4", "simplex-noise": "^4.0.3", "svelte-motion": "^0.12.2"}}