<script lang="ts">
  import * as Sidebar from "$lib/components/ui/sidebar";
  import { Separator } from "$lib/components/ui/separator";
  import AppBreadcrumb from './app-breadcrumb.svelte';
  import { getEntityIconByName, useAppStore } from "$lib/app-store.svelte";

  const app = useAppStore();
  const EntityIcon = $derived(getEntityIconByName(app.ActiveEntity.name));
  const sidebar = Sidebar.useSidebar();

</script>

<header class="flex h-16 shrink-0 items-center gap-2 sticky top-0 bg-white z-10 mb-4 shadow-sm header-acrylic">
  <div class="flex items-center gap-2 px-4">
    <Sidebar.Trigger class="-ml-1" />
    <Separator orientation="vertical" class="mr-2 h-4" />
    {#if sidebar.isMobile}
      <EntityIcon class="size-6"/>
    {/if}
    <AppBreadcrumb data={app.CurrentBreadcrumb}/>
</div>
</header>

<style>
  .header-acrylic {
    backdrop-filter: blur(20px);
    background-color: rgba(255, 255, 255, 0.5);
  }
</style>