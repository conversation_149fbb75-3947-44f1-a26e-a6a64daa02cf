<script lang="ts">
    import * as DropdownMenu from "$lib/components/ui/dropdown-menu";
    import * as Sidebar from "$lib/components/ui/sidebar";
    import { ChevronsUpDown } from 'lucide-svelte';
    import { useAppStore, getEntityIconByName } from '$lib/app-store.svelte';
    import { Entities, type Entity} from '$lib/report';

    const sidebar = Sidebar.useSidebar();
    const app = useAppStore();
    const EntityIcon = $derived(getEntityIconByName(app.ActiveEntity.name));

    const EntityList = Entities.map((ent: Entity) => {
        return {
            ...ent,
            icon: getEntityIconByName(ent.name),
        };
    });

    const entityNameList = Entities.map(ent => ent.name);

    function onEntityChange(entityName: string) {
        if (entityName === app.ActiveEntity.name) {
            console.debug('Entity already active');
            return;
        }

        app.setActiveEntity(entityName);
        document.documentElement.classList.remove(
            ...entityNameList.map(color => `theme-${color}`),
        )
        document.documentElement.classList.add(`theme-${entityName}`);
        if (sidebar.isMobile) {
            sidebar.toggle();
        }
    }

</script>

<DropdownMenu.Root>
    <DropdownMenu.Trigger>
        {#snippet child({ props })}
            <Sidebar.MenuButton
                {...props}
                size="lg"
                class="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
                <EntityIcon class="size-8" />
                <div class="grid flex-1 text-left text-sm leading-tight">
                    <span class="truncate font-semibold">{app.ActiveEntity.name}</span>
                    <span class="truncate text-xs">{app.ActiveEntity.desc}</span>
                </div>
                <ChevronsUpDown class="ml-auto size-4" />
            </Sidebar.MenuButton>
        {/snippet}
    </DropdownMenu.Trigger>
    <DropdownMenu.Content
        class="w-[--bits-dropdown-menu-anchor-width] min-w-56 rounded-lg"
        side={sidebar.isMobile ? "bottom" : "top"}
        align="end"
        sideOffset={4}
    >
        <DropdownMenu.Group>
            {#each EntityList as ent}
                <DropdownMenu.Item onclick={() => onEntityChange(ent.name)}>
                    <ent.icon class="size-4" />
                    {ent.name}
                </DropdownMenu.Item>
            {/each}
        </DropdownMenu.Group>
    </DropdownMenu.Content>
</DropdownMenu.Root>