<script lang="ts">
  import * as Dialog from '$lib/components/ui/dialog';
  import { DollarSign, Users, Store, ArrowDownUp } from 'lucide-svelte';
  import DataTable from '$lib/components/dashboard/data-table/data-table.svelte';
  import MtdKpiCard from '$lib/components/dashboard/mtd-kpi-card.svelte';
  import MtdEntityDataTableCard from '$lib/components/dashboard/mtd-entity-data-table-card.svelte';
	import { createMtdKpiDataTableColumns } from '$lib/components/dashboard/mtd-entity-data-table';

  import { useReportService, getMtd, formatToMonthID, type KpiDTO, type CoreKpiDTO } from '$lib/report';
  import { useAppStore } from "$lib/app-store.svelte";

  import { getCircleMtdKpi, getNationalMtdKpi, getRegionMtdKpi, MtdKpiToMtdKpiValue, type MTDKpi, type mtdKpis } from './mtd-util';
	import type { MtdKpiValue } from "$lib/data/kpi/mtd";
	import { useReportStore } from '$lib/report/report.store.svelte';
  
  const report = useReportService();
  const repStore = useReportStore();
  const app = useAppStore();
  
  const { mtd } = getMtd();
  
  let mtdNationalIsFetched = $state(false);
  let mtdCircleIsFetched = $state(false);
  let mtdRegionIsFetched = $state(false);

  let regionMtdDialogIsOpen = $state(false);
  let activeRegionMtd = $state<{kpi_name?: string, unit?: string, kpis?: MtdKpiValue[]}>({});
  
  let mtdNationalData = $state<mtdKpis>();
  let mtdCircleData = $state<Map<string, mtdKpis>>();
  let mtdRegionData = $state<Map<string, Map<string, mtdKpis>>>();

  let revenueNational = $derived<MTDKpi | undefined>(mtdNationalData?.revenue);
  let subsNational = $derived<MTDKpi | undefined>(mtdNationalData?.subs);
  let grossAddNational = $derived<MTDKpi | undefined>(mtdNationalData?.grossAdd);
  let trafficNational = $derived<MTDKpi | undefined>(mtdNationalData?.traffic);

  const revenueCircle = $derived.by(() => {
    return makeCircleKpi('revenue', mtdCircleData ?? new Map<string, mtdKpis>());
  });

  const subsCircle = $derived.by(() => {
    return makeCircleKpi('subs', mtdCircleData ?? new Map<string, mtdKpis>());
  });

  const grossAddCircle = $derived.by(() => {
    return makeCircleKpi('grossAdd', mtdCircleData ?? new Map<string, mtdKpis>());
  });

  const trafficCircle = $derived.by(() => {
    return makeCircleKpi('traffic', mtdCircleData ?? new Map<string, mtdKpis>());
  });

  function makeCircleKpi(mtdkpi_name: 'revenue' | 'subs' | 'grossAdd' | 'traffic', kpi: Map<string, mtdKpis>): MtdKpiValue[] {
    const values = Array.from(kpi.entries()).map(([key, value]) => {
      return MtdKpiToMtdKpiValue(key, value[mtdkpi_name]);
    });

    return values.sort((a, b) => {
      const aname = a.entity_name ?? 'Null';
      const bname = b.entity_name ?? 'Null';
      if (aname === 'Null') {
        return 1;
      }

      if (bname === 'Null') {
        return -1;
      }

      return aname.localeCompare(bname);
    });

  }

  $effect(() => {
    mtdNationalIsFetched = false;
    const entity = app.ActiveEntity.name;
    const cachedData = repStore.getCacheItem('comm_dash001', entity, 'mtd_national_kpi');
    if (cachedData) {
      mtdNationalData = getNationalMtdKpi(cachedData)
      mtdNationalIsFetched = true;
      return;
    }

    report.getReport({
      bu_id: 'Core',
      entity: entity,
      report_id: 'comm_dash001',
      t_instance_id: 'MTD',
      grouping_id: 'National',
      end_time: mtd.toString(),
    }).then((data) => {
      mtdNationalData = getNationalMtdKpi(data);
      repStore.setCacheItem('comm_dash001', entity, 'mtd_national_kpi', data);
      mtdNationalIsFetched = true;
    });
  })

  $effect(() => {
    mtdCircleIsFetched = false;
    const entity = app.ActiveEntity.name;
    const cachedData = repStore.getCacheItem('comm_dash001', entity, 'mtd_circle_kpi');
    if (cachedData) {
      mtdCircleData = getCircleMtdKpi(cachedData);
      mtdCircleIsFetched = true;
      return;
    }

    report.getReport({
      bu_id: 'Core',
      entity: entity,
      report_id: 'comm_dash001',
      t_instance_id: 'MTD',
      grouping_id: 'Circle',
      end_time: mtd.toString(),
    }).then((data) => {
      mtdCircleData = getCircleMtdKpi(data);
      repStore.setCacheItem('comm_dash001', entity, 'mtd_circle_kpi', data);
      mtdCircleIsFetched = true;
    });
  });

  function processRegionData(rdata: KpiDTO<CoreKpiDTO>[]) {
    const circles = new Set<string | undefined>();
    Array.from(rdata?.values() ?? []).forEach((kpi) => {
      circles.add(kpi.identifier.circle);
    });
    
    const mtdData = new Map<string, Map<string, mtdKpis>>();
    for (const circle of circles) {
      const regionData = getRegionMtdKpi(rdata, circle);
      
      mtdData.set(circle ?? 'Null', regionData);
    }

    mtdRegionData = mtdData;
  }

  $effect(() => {
    mtdRegionIsFetched = false;
    const entity = app.ActiveEntity.name;
    
    const cachedData = repStore.getCacheItem('comm_dash001', entity, 'mtd_region_kpi');
    if (cachedData) {
      processRegionData(cachedData);
      mtdRegionIsFetched = true;
      return;
    }
      
    report.getReport({
      bu_id: 'Core',
      entity: entity,
      report_id: 'comm_dash001',
      t_instance_id: 'MTD',
      grouping_id: 'Region',
      end_time: mtd.toString(),
    }).then((data) => {
      processRegionData(data);
      repStore.setCacheItem('comm_dash001', entity, 'mtd_region_kpi', data);
      mtdRegionIsFetched = true;
    });

  });

  function onCircleSelect(data: MtdKpiValue) {
    regionMtdDialogIsOpen = false;
    let kpi_name = '';
    let unit = '';
    let kpi_data: MtdKpiValue[] = [];
    let kpis: MtdKpiValue[] = [];
    const entity = data.entity_name ?? '';
    const region_data = mtdRegionData?.get(entity) ?? new Map<string, mtdKpis>();

    switch (data.kpi_name) {
      case 'REVENUE':
        kpi_name = 'Revenue';
        unit = 'Billion';
        kpis = Array.from(region_data?.entries() ?? []).map(([region, kpi]) => MtdKpiToMtdKpiValue(region, kpi.revenue));
        break;
      case 'SUBS':
        kpi_name = 'RGU 90D';
        unit = 'Million';
        kpis = Array.from(region_data?.entries() ?? []).map(([region, kpi]) => MtdKpiToMtdKpiValue(region, kpi.subs));
        break;
      case 'GROSS_ADD':
        kpi_name = 'Gross Adds';
        unit = 'Thousand';
        kpis = Array.from(region_data?.entries() ?? []).map(([region, kpi]) => MtdKpiToMtdKpiValue(region, kpi.grossAdd));
        break;
      case 'TRAFFIC':
        kpi_name = 'Traffic';
        unit = 'Tera Bytes';
        kpis = Array.from(region_data?.entries() ?? []).map(([region, kpi]) => MtdKpiToMtdKpiValue(region, kpi.traffic));
        break;
      default:
        break;
    }

    activeRegionMtd = {
      kpi_name,
      unit,
      kpis
    };

    regionMtdDialogIsOpen = true;
  }

</script>

<Dialog.Root bind:open={regionMtdDialogIsOpen}>
  <Dialog.Portal>
    <Dialog.Content>
      <Dialog.Title>{activeRegionMtd.kpi_name}</Dialog.Title>
      <Dialog.Description>{`Region wise ${activeRegionMtd.kpi_name} in ${activeRegionMtd.unit}`}</Dialog.Description>
      <DataTable
      data={activeRegionMtd.kpis ?? []}
      columns={createMtdKpiDataTableColumns('Region')}/>
    </Dialog.Content>
  </Dialog.Portal>
</Dialog.Root>

<div class="grid grid-cols-1 auto-rows-min gap-4 md:grid-cols-4">
  <MtdKpiCard 
    is-loading={!mtdNationalIsFetched} 
    kpi-name="Total Revenue"
    mtd-text={revenueNational?.mtd?.value_text ?? '0'}
    mtd-date={revenueNational?.mtd?.mtd_date}
    growth-text={revenueNational?.growthText} icon={DollarSign} 
    lmtd-text={revenueNational?.lmtd?.value_text}
    lmtd-date={revenueNational?.lmtd?.mtd_date}/>
  
  <MtdKpiCard 
    is-loading={!mtdNationalIsFetched}
    kpi-name="RGU 90D" 
    mtd-text={subsNational?.mtd?.value_text ?? '0'}
    mtd-date={subsNational?.mtd?.mtd_date}
    growth-text={subsNational?.growthText} icon={Users}
    lmtd-text={subsNational?.lmtd?.value_text}
    lmtd-date={subsNational?.lmtd?.mtd_date} />
  
  <MtdKpiCard
    is-loading={!mtdNationalIsFetched} 
    kpi-name="Gross Adds" 
    mtd-text={grossAddNational?.mtd?.value_text ?? '0'} 
    mtd-date={grossAddNational?.mtd?.mtd_date}
    growth-text={grossAddNational?.growthText} icon={Store}
    lmtd-text={grossAddNational?.lmtd?.value_text}
    lmtd-date={grossAddNational?.lmtd?.mtd_date} />
  
  <MtdKpiCard
    is-loading={!mtdNationalIsFetched}
    kpi-name="Traffic" 
    mtd-text={trafficNational?.mtd?.value_text ?? '0'} 
    mtd-date={trafficNational?.mtd?.mtd_date}
    growth-text={trafficNational?.growthText} icon={ArrowDownUp}
    lmtd-text={trafficNational?.lmtd?.value_text}
    lmtd-date={trafficNational?.lmtd?.mtd_date} />
  
  <MtdEntityDataTableCard
    is-loading={!mtdCircleIsFetched}
    title="Revenue"
    subtitle="Circle Wise Revenue in Billion"
    entity-title="Circle"
    onrowclick={onCircleSelect}
    data={revenueCircle} />
  
  <MtdEntityDataTableCard
    is-loading={!mtdCircleIsFetched}
    title="RGU 90D"
    subtitle="Circle Wise RGU90D in Million"
    entity-title="Circle"
    onrowclick={onCircleSelect}
    data={subsCircle} />
  
  <MtdEntityDataTableCard
    is-loading={!mtdCircleIsFetched}
    title="Gross Adds"
    subtitle="Circle Wise Gross Adds in Thousand"
    entity-title="Circle"
    onrowclick={onCircleSelect}
    data={grossAddCircle} />
  
  <MtdEntityDataTableCard
    is-loading={!mtdCircleIsFetched}
    title="Traffic"
    subtitle="Circle Wise Traffic in Tera Bytes"
    entity-title="Circle"
    onrowclick={onCircleSelect}
    data={trafficCircle} />
  
</div>