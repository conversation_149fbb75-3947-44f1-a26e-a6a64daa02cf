import type { ChartSeries, Series } from '$lib/components/dashboard/charts';
import type { FmCircleKpiValue, FmKpiValue } from '$lib/data/kpi/fullmonth';
import { type ValueConfig, KpiUtil } from '$lib/data/kpi/util';
import type { Circle, CoreKpiDTO, KpiDTO } from '$lib/report';
import { formatBytes, formatDateToMonthYear, formatMetric, RoundFloat } from '$lib/utils/formatter';

export type KpiField = 'rev_total' | 'rgu_90' | 'rgs_ga' | 'traffic_gb';
const kpiNameMap: Record<KpiField, string> = {
    'rev_total': 'Revenue',
    'rgu_90': 'RGU 90D',
    'rgs_ga': 'Gross Add',
    'traffic_gb': 'Traffic'
}

const UNITS = {
    REVENUE: { unit: 'Bn', divider: 1000000000, precision: 1 },
    SUBS: { unit: 'Mn', divider: 1000000, precision: 1 },
    GROSS_ADD: { unit: 'Mn', divider: 1000_000, precision: 1 },
    TRAFFIC: { unit: 'PB', divider: 1024 * 1024, formatter: formatBytes }
} as const;

export function getNationalFmKpi(kpis: KpiDTO<CoreKpiDTO>[]): FmKpiValue[] {
    const fmKpi: FmKpiValue[] = [];
    if (kpis.length === 0) return [];

    for (let i = 0; i < kpis.length; i++) {
        if (kpis[i].time_instance.type !== 'FullMonth') continue;

        fmKpi.push(createFmKpiValue(kpis[i], 'rev_total', UNITS.REVENUE))
        fmKpi.push(createFmKpiValue(kpis[i], 'rgu_90', UNITS.SUBS))
        fmKpi.push(createFmKpiValue(kpis[i], 'rgs_ga', UNITS.GROSS_ADD))
        fmKpi.push(createFmKpiValue(kpis[i], 'traffic_gb', UNITS.TRAFFIC))
    }

    return fmKpi.sort((a, b) => a.month_date - b.month_date);
}

export function getCircleChartFmKpi(kpis: KpiDTO<CoreKpiDTO>[]) {
    const revenue = createFmKpiChartEntitySeries('rev_total', kpis, UNITS.REVENUE);
    const traffic = createFmKpiChartEntitySeries('traffic_gb', kpis, UNITS.TRAFFIC);
    const subs = createFmKpiChartEntitySeries('rgu_90', kpis, UNITS.SUBS);
    const grossAdd = createFmKpiChartEntitySeries('rgs_ga', kpis, UNITS.GROSS_ADD);

    return {
        revenue,
        traffic,
        subs,
        grossAdd
    }
}

function createFmKpiValue(kpi: KpiDTO<CoreKpiDTO>, kpiField: KpiField, conf: ValueConfig): FmKpiValue {
    let entity: string = kpi.identifier.entity;
    switch (kpi.identifier.grouping) {
        case 'Circle':
            entity = kpi.identifier.circle ?? 'Null';
            break;
        case 'Region':
            entity = kpi.identifier.region ?? 'Null';
        default:
            break;
    }
    const sentity = kpi.identifier.entity;
    const month_date = kpi.time_instance.value;
    const month_name = formatDateToMonthYear(month_date);
    
    const kpiValue = KpiUtil.calculateKpiValue(kpiNameMap[kpiField], {value: kpi.kpi[kpiField], ...conf});
    return {
        entity_name: entity,
        month_date: month_date,
        month_name: month_name,
        ...kpiValue
    };
}

export function createFmKpiChartEntitySeries(
    kpi_field: keyof KpiDTO<CoreKpiDTO>['kpi'], 
    kpis: KpiDTO<CoreKpiDTO>[],
    conf: ValueConfig
): ChartSeries {
    const categoriesSet = new Set<number>();
    const seriesNamesSet = new Set<string>();
    const seriesMap: Map<string, [number, number][]> = new Map();
    kpis.forEach(kpi => {
        if (kpi.time_instance.type === 'FullMonth') {
            const month_date = kpi.time_instance.value;
            const kpi_value = KpiUtil.calculateKpiValue('', {value: kpi.kpi[kpi_field], ...conf});
            categoriesSet.add(month_date);
            let seriesName: string;
            switch (kpi.identifier.grouping) {
                case 'National':
                    seriesName = kpi.identifier.entity;
                    break;
                case 'Circle':
                    seriesName = kpi.identifier.circle ?? 'Null';
                    break;
                case 'Region':
                    seriesName = kpi.identifier.region ?? 'Null';
                    break;
                default:
                    seriesName = 'Null';
                    break;
            }

            if (seriesName === 'Null' && (kpi.kpi[kpi_field] ?? 0) == 0) return;
            if (!seriesMap.has(seriesName)) {
                seriesMap.set(seriesName, []);
                seriesNamesSet.add(seriesName);
            }

            seriesMap.get(seriesName)?.push([month_date, kpi_value.value ?? 0]);
        }
    });

    const series = Array.from(seriesMap.entries()).map(([name, data]) => ({name, unit: conf.unit, data: data.sort((a, b) => a[0] - b[0])}));
    return {
        categories: Array.from(categoriesSet).sort((a, b) => a - b),
        series
    }
}