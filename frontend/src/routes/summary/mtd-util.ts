import type { KpiValue } from '$lib/data/kpi/base';
import { MtdUtil, type MtdKpiItem, type MtdKpiValue } from '$lib/data/kpi/mtd';
import { type ValueConfig, KpiUtil} from '$lib/data/kpi/util';
import type{ CoreKpiDTO, KpiDTO } from '$lib/report';
import { formatBytes, formatDateToMonthYear, formatMetric, RoundFloat } from '$lib/utils/formatter';

export type KpiField = 'rev_total' | 'rgu_90' | 'rgs_ga' | 'traffic_gb';
export interface MTDKpi {
    mtd?: MtdKpiItem;
    lmtd?: MtdKpiItem;
    growth?: number;
    growthText?: string;
}

export interface mtdKpis {
    mtdMonth?: string;
    lmtdMonth?: string;
    revenue?: MTDKpi;
    subs?: MTDKpi;
    grossAdd?: MTDKpi;
    traffic?: MTDKpi;
}

const UNITS = {
    REVENUE: { unit: 'Bn', divider: 1000000000, precision: 1 },
    SUBS: { unit: 'Mn', divider: 1000000, precision: 1 },
    GROSS_ADD: { unit: 'Unit', divider: 1 },
    TRAFFIC: { unit: 'TB', divider: 1024 * 1024 * 1024 * 1024, formatter: formatBytes }
} as const;

export function getNationalMtdKpi(kpi: KpiDTO<CoreKpiDTO>[]): mtdKpis {
    if (kpi.length === 0) return {};

    const mtdKpi = kpi.find(k => k.time_instance.type === 'MTD' && k.time_instance.name === 'MTD');
    const lmtdKpi = kpi.find(k => k.time_instance.type === 'MTD' && k.time_instance.name === 'LMTD');

    const mtd: mtdKpis = mtdKpi ? processMtdData(mtdKpi) : {};

    if (lmtdKpi && mtd) {
        processLmtdData(lmtdKpi, mtd);
    }

    processGrowth(mtd);
    return mtd;
}

export function getCircleMtdKpi(kpi: KpiDTO<CoreKpiDTO>[]): Map<string, mtdKpis> {
    // const circleMtdKpi = new Map<string, mtdKpis>();

    // const mtdKpis = kpi.filter(k => k.time_instance.type === 'MTD' && k.time_instance.name === 'MTD');
    // const lmtdKpis = kpi.filter(k => k.time_instance.type === 'MTD' && k.time_instance.name === 'LMTD');

    // for (const mtdKpi of mtdKpis) {
    //     const circleId = mtdKpi.identifier.circle;
    //     // if (!circleId) continue;

    //     const circleMtd = processMtdData(mtdKpi);

    //     const lmtdKpi = lmtdKpis.find(lmtd => lmtd.identifier.circle === circleId);
    //     if (lmtdKpi) {
    //         processLmtdData(lmtdKpi, circleMtd);
    //     }

    //     processGrowth(circleMtd);
    //     circleMtdKpi.set(circleId??'Null', circleMtd);
    // }

    // return circleMtdKpi;
    return getMtdKpiMap('circle', kpi);
}

export function getRegionMtdKpi(kpi: KpiDTO<CoreKpiDTO>[], circle?: string): Map<string, mtdKpis> {
    const regionMap = new Map<string, mtdKpis>();
    kpi = kpi.filter(k => k.identifier.circle === circle);

    return getMtdKpiMap('region', kpi);
}

function getMtdKpiMap(entity_name: 'circle' | 'region', kpi: KpiDTO<CoreKpiDTO>[]): Map<string, mtdKpis> {
    const mtdKpiMap = new Map<string, mtdKpis>();

    const mtdKpis = kpi.filter(k => k.time_instance.type === 'MTD' && k.time_instance.name === 'MTD');
    const lmtdKpis = kpi.filter(k => k.time_instance.type === 'MTD' && k.time_instance.name === 'LMTD');

    for (const mtdKpi of mtdKpis) {
        const entityId = mtdKpi.identifier[entity_name];
        // if (!entityId) continue;

        const entityMtd = processMtdData(mtdKpi);

        const lmtdKpi = lmtdKpis.find(lmtd => lmtd.identifier[entity_name] === entityId);
        if (lmtdKpi) {
            processLmtdData(lmtdKpi, entityMtd);
        }

        processGrowth(entityMtd);
        mtdKpiMap.set(entityId ?? 'Null', entityMtd);
    }

    return mtdKpiMap;
}

function initializeMtdKpi(): MTDKpi {
    return { growthText: 'no data', growth: 0 };
}

function processMtdData(kpis: KpiDTO<CoreKpiDTO>): Partial<mtdKpis> {
    const mtdData: mtdKpis = {
        mtdMonth: formatDateToMonthYear(kpis.time_instance.value),
    };

    // Process Revenue
    mtdData.revenue = initializeMtdKpi();
    mtdData.revenue.mtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("REVENUE", {value: kpis.kpi.rev_total, ...UNITS.REVENUE}),
        kpis.time_instance.name,
        kpis.time_instance.value
    )

    // Process Subs
    mtdData.subs = initializeMtdKpi();
    mtdData.subs.mtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("SUBS", {value: kpis.kpi.rgu_90, ...UNITS.SUBS}),
        kpis.time_instance.name,
        kpis.time_instance.value
    )

    // Process Gross Add
    mtdData.grossAdd = initializeMtdKpi();
    mtdData.grossAdd.mtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("GROSS_ADD", {value: kpis.kpi.rgs_ga, ...UNITS.GROSS_ADD}),
        kpis.time_instance.name,
        kpis.time_instance.value
    )

    // Process Traffic
    mtdData.traffic = initializeMtdKpi();
    mtdData.traffic.mtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("TRAFFIC", {value: (kpis.kpi.traffic_gb ?? 0) * 1024 * 1024 * 1024, ...UNITS.TRAFFIC}),
        kpis.time_instance.name,
        kpis.time_instance.value
    )

    return mtdData;
}

function processLmtdData(lmtdKpis: KpiDTO<CoreKpiDTO>, mtdData: mtdKpis): void {
    mtdData.lmtdMonth = formatDateToMonthYear(lmtdKpis.time_instance.value);

    // Process Revenue LMTD
    if (!mtdData.revenue) mtdData.revenue = initializeMtdKpi();
    mtdData.revenue.lmtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("REVENUE", {value: lmtdKpis.kpi.rev_total, ...UNITS.REVENUE}),
        lmtdKpis.time_instance.name,
        lmtdKpis.time_instance.value
    )

    // Process Subs LMTD
    if (!mtdData.subs) mtdData.subs = {};
    mtdData.subs.lmtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("SUBS", {value: lmtdKpis.kpi.rgu_90, ...UNITS.SUBS}),
        lmtdKpis.time_instance.name,
        lmtdKpis.time_instance.value
    )

    // Process Gross Add LMTD
    if (!mtdData.grossAdd) mtdData.grossAdd = {};
    mtdData.grossAdd.lmtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("GROSS_ADD", {value: lmtdKpis.kpi.rgs_ga, ...UNITS.GROSS_ADD}),
        lmtdKpis.time_instance.name,
        lmtdKpis.time_instance.value
    )

    // Process Traffic LMTD
    if (!mtdData.traffic) mtdData.traffic = {};
    mtdData.traffic.lmtd = MtdUtil.createMtdKpiItem(
        KpiUtil.calculateKpiValue("TRAFFIC", {value: (lmtdKpis.kpi.traffic_gb ?? 0) * 1024 * 1024 * 1024, ...UNITS.TRAFFIC}),
        lmtdKpis.time_instance.name,
        lmtdKpis.time_instance.value
    )
    
}

function processGrowth(mtd: mtdKpis): void {
    if (mtd.revenue?.mtd?.value && mtd.revenue.lmtd?.value) {
        const { growth, growth_text } = KpiUtil.createGrowth(mtd.revenue.mtd.value, mtd.revenue.lmtd.value);
        
        mtd.revenue.growth = growth;
        mtd.revenue.growthText = growth_text;
    }

    if (mtd.subs?.mtd?.value && mtd.subs.lmtd?.value) {
        const { growth, growth_text } = KpiUtil.createGrowth(mtd.subs.mtd.value, mtd.subs.lmtd.value);
        mtd.subs.growth = growth;
        mtd.subs.growthText = growth_text;
    }

    if (mtd.grossAdd?.mtd?.value && mtd.grossAdd.lmtd?.value) {
        const { growth, growth_text } = KpiUtil.createGrowth(mtd.grossAdd.mtd.value, mtd.grossAdd.lmtd.value);
        mtd.grossAdd.growth = growth;
        mtd.grossAdd.growthText = growth_text;
    }

    if (mtd.traffic?.mtd?.value && mtd.traffic.lmtd?.value) {
        const { growth, growth_text } = KpiUtil.createGrowth(mtd.traffic.mtd.value, mtd.traffic.lmtd.value);
        mtd.traffic.growth = growth;
        mtd.traffic.growthText = growth_text;
    }
}

export function MtdKpiToMtdKpiValue(entity_name: string, kpi: MTDKpi | undefined): MtdKpiValue {
    return {
        kpi_name: kpi?.mtd?.kpi_name ?? '',
        entity_name: entity_name,
        unit: kpi?.mtd?.unit ?? '',
        mtd_value: kpi?.mtd?.value,
        mtd_value_text: kpi?.mtd?.value_text,
        mtd_date: kpi?.mtd?.mtd_date,
        lmtd_value: kpi?.lmtd?.value,
        lmtd_value_text: kpi?.lmtd?.value_text,
        lmtd_date: kpi?.lmtd?.mtd_date,
        growth: kpi?.growth,
        growth_text: kpi?.growthText
    }
}