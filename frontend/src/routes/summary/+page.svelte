<script lang="ts">
  import { fade } from 'svelte/transition';
  import * as date from '@internationalized/date';
  import * as Tabs from '$lib/components/ui/tabs';

  import MtdSummaryView from "./mtd-summary-view.svelte";
  import FmSummaryView from "./monthly-summary-view.svelte";

  let activeTab = $state('mtd');
  
</script>

<div class="flex flex-1 flex-col gap-4 pl-4 pr-4 pt-0">
  <Tabs.Root bind:value={activeTab}>
    <Tabs.List>
      <Tabs.Trigger value="mtd">MTD</Tabs.Trigger>
      <Tabs.Trigger value="monthly">Monthly</Tabs.Trigger>
    </Tabs.List>
  </Tabs.Root>
  
  <div class="flex flex-1 flex-col gap-4">
  {#if activeTab === 'mtd'}
    <div>
      <MtdSummaryView/>
    </div>
  {:else if activeTab === 'monthly'}
    <div>
      <FmSummaryView/>
    </div>
  {/if}
  </div>
</div>