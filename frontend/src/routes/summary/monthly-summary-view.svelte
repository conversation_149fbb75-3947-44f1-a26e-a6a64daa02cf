<script lang="ts">
	import { useAppStore } from "$lib/app-store.svelte";
	import type { FmKpiValue } from "$lib/data/kpi/fullmonth";
	import { useReportService, type CoreKpiDTO, type KpiDTO } from "$lib/report";
  import { DollarSign, Users, Store, ArrowDownUp } from 'lucide-svelte';
  import * as date from '@internationalized/date';
	import { getNationalFmKpi, createFmKpiChartEntitySeries, getCircleChartFmKpi } from "./fm-util";
	import FmKpiTableCard from "$lib/components/dashboard/fm-kpi-table-card.svelte";
  import FmChartCard from "$lib/components/dashboard/fm-chart-card.svelte";
  import * as Chart from "$lib/components/dashboard/charts";
  import { Badge } from '$lib/components/ui/badge';
  import SimpleCard from "$lib/components/dashboard/simple-card.svelte";
	import { useReportStore } from "$lib/report/report.store.svelte";
	import { formatToMonthID, formatDateToMonthYear } from "$lib/utils/formatter";

  const today = date.today(date.getLocalTimeZone());
  const lastMonth = today.subtract({months: 1});
  const startMonth = date.startOfMonth(lastMonth.subtract({months: 12})).toDate(date.getLocalTimeZone()).valueOf();
  // const startMonth = date.startOfYear(today).toDate(date.getLocalTimeZone()).valueOf();
  const endMonth = date.endOfMonth(lastMonth).toDate(date.getLocalTimeZone()).valueOf();

  // const start_month = formatToMonthID(date.fromDate(new Date(startMonth), date.getLocalTimeZone()));
  const start_month = formatToMonthID(startMonth)
  const end_month = formatToMonthID(endMonth);

  const report = useReportService();
  const repStore = useReportStore();
  const app = useAppStore();

  let fm_national_data = $state<FmKpiValue[]>();
  let fm_circle_data = $state<Map<string, FmKpiValue[]>>();
  let nationalDataIsFetched = $state(false);
  let circleDataIsFetched = $state(false);

  let revenueCircle = $state<Chart.ChartSeries>();
  let trafficCircle = $state<Chart.ChartSeries>();
  let subsCircle = $state<Chart.ChartSeries>();
  let grossAddCircle = $state<Chart.ChartSeries>();

  $effect(() => {
    nationalDataIsFetched = false;

    const entity = app.ActiveEntity.name;
    const nationalCachedData = repStore.getCacheItem('comm_dash001', entity, 'monthly_national_kpi')

    if (nationalCachedData) {
      fm_national_data = getNationalFmKpi(nationalCachedData)
      nationalDataIsFetched = true;
      return;
    }

    report.getReport({
      start_time: start_month,
      end_time: end_month,
      grouping_id: 'National',
      entity: app.ActiveEntity.name,
      report_id: 'comm_dash001',
      t_instance_id: 'FullMonth',
      bu_id: 'Core',
    }).then(data => {
      fm_national_data = getNationalFmKpi(data);
      repStore.setCacheItem('comm_dash001', entity, 'monthly_national_kpi', data);
      nationalDataIsFetched = true;
    });

  });

  $effect(() => {
    circleDataIsFetched = false;

    const entity = app.ActiveEntity.name;
    const circleCachedData = repStore.getCacheItem('comm_dash001', entity, 'monthly_circle_kpi')

    if (circleCachedData) {
      populateCircleData(circleCachedData);
      circleDataIsFetched = true;
      return;
    }

    report.getReport({
      start_time: start_month,
      end_time: end_month,
      grouping_id: 'Circle',
      entity: app.ActiveEntity.name,
      report_id: 'comm_dash001',
      t_instance_id: 'FullMonth',
      bu_id: 'Core',
    }).then(data => {
      populateCircleData(data);
      repStore.setCacheItem('comm_dash001', entity, 'monthly_circle_kpi', data);
      circleDataIsFetched = true;
    });
  })

  function populateCircleData(data: KpiDTO<CoreKpiDTO>[]) {
    const circleData = getCircleChartFmKpi(data);
    revenueCircle = circleData.revenue;
    trafficCircle = circleData.traffic;
    subsCircle = circleData.subs;
    grossAddCircle = circleData.grossAdd;
  }
  
</script>

<div class="grid grid-cols-1 auto-rows-min gap-4 md:grid-cols-4">
  <FmChartCard
    data={fm_national_data?.filter(kpi => kpi.kpi_name === 'Revenue')}
    is-loading={!nationalDataIsFetched}
    kpi-name="National Revenue"
    icon={DollarSign}
  />

  <FmChartCard
    data={fm_national_data?.filter(kpi => kpi.kpi_name === 'RGU 90D')}
    is-loading={!nationalDataIsFetched}
    kpi-name="National RGU 90D"
    icon={Users}
  />

  <FmChartCard
    data={fm_national_data?.filter(kpi => kpi.kpi_name === 'Gross Add')}
    is-loading={!nationalDataIsFetched}
    kpi-name="National Gross Add"
    icon={Store}
  />

  <FmChartCard
    data={fm_national_data?.filter(kpi => kpi.kpi_name === 'Traffic')}
    is-loading={!nationalDataIsFetched}
    kpi-name="National Traffic"
    icon={ArrowDownUp}
  />

  <FmKpiTableCard
    class="md:col-span-4"
    title="National KPI"
    subtitle="Full Month"
    data={fm_national_data ?? []}
    is-loading={!nationalDataIsFetched}
    start-month={startMonth}
    end-month={endMonth} />

  <SimpleCard>
    {#snippet title()}
      <Badge class="w-full h-8 justify-center text-sm">Revenue</Badge>
    {/snippet}
    <Chart.Bar
      data={revenueCircle}
      yAxis={{label: 'Billion'}}
      xAxis={{tickFormat: (d: number | Date) => formatDateToMonthYear(d)}}
      height={230}
      is-loading={!circleDataIsFetched}
    />
  </SimpleCard>

  <SimpleCard>
    {#snippet title()}
      <Badge class="w-full h-8 justify-center text-sm">RGU 90D</Badge>
    {/snippet}
    <Chart.Bar
      data={subsCircle}
      yAxis={{label: 'Million'}} 
      xAxis={{tickFormat: (d: number | Date) => formatDateToMonthYear(d)}}
      height={230}
      is-loading={!circleDataIsFetched}
    />
  </SimpleCard>

  <SimpleCard>
    {#snippet title()}
      <Badge class="w-full h-8 justify-center text-sm">Gross Add</Badge>
    {/snippet}
    <Chart.Bar
      data={grossAddCircle}
      yAxis={{label: 'Million'}} 
      xAxis={{tickFormat: (d: number | Date) => formatDateToMonthYear(d)}}
      height={230}
      is-loading={!circleDataIsFetched}
    />
  </SimpleCard>

  <SimpleCard>
    {#snippet title()}
      <Badge class="w-full h-8 justify-center text-sm">Traffic</Badge>
    {/snippet}
    <Chart.Bar
      data={trafficCircle}
      yAxis={{label: 'Penta Bytes'}} 
      xAxis={{tickFormat: (d: number | Date) => formatDateToMonthYear(d)}}
      height={230}
      is-loading={!circleDataIsFetched}
    />
  </SimpleCard>
</div>
