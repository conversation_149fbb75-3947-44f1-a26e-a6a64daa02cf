<script lang="ts">
  import { page } from '$app/stores';
	import * as Collapsible from "$lib/components/ui/collapsible";
	import * as Sidebar from "$lib/components/ui/sidebar";
	import { ChevronRight, Gauge } from "lucide-svelte";
	import { CoreDashboard, DigitalDashboard } from './nav';
  import { slide } from 'svelte/transition';

</script>

<Sidebar.Group>
  <Sidebar.Menu>
    <Sidebar.MenuItem>
      <Sidebar.MenuButton class={$page.url.pathname === '/summary' ? 'bg-sidebar-accent' : ''}>
        <Gauge/>
        <a href="/summary">Summary</a>
      </Sidebar.MenuButton>
    </Sidebar.MenuItem>
  </Sidebar.Menu>
</Sidebar.Group>

<Sidebar.Group>
  <Sidebar.GroupLabel>Core</Sidebar.GroupLabel>
  <Sidebar.Menu>
    {#each CoreDashboard as item, idx (idx)}
      <Collapsible.Root class="group/collapsible" open={item.isActive}>
        {#snippet child({ props })}
          <Sidebar.MenuItem {...props}>
            <Collapsible.Trigger>
              {#snippet child({ props })}
                <Sidebar.MenuButton {...props}>
                  {#snippet tooltipContent()}
                    {item.title}
                  {/snippet}
                  <item.icon />
                  <span>{ item.title }</span> 
                  <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </Sidebar.MenuButton>
              {/snippet}
            </Collapsible.Trigger>
            <Collapsible.Content forceMount>
              {#snippet child({ props, open })}
                {#if open}
                  <div {...props} transition:slide>
                    <Sidebar.MenuSub>
                      {#each item.items as subItem, idx (idx)}
                        <Sidebar.MenuSubItem>
                          <Sidebar.MenuSubButton class={$page.url.pathname === subItem.page ? 'bg-sidebar-accent' : ''}>
                            {#snippet child({ props})}
                              <a href={subItem.page} {...props}>{ subItem.title }</a>
                            {/snippet}
                          </Sidebar.MenuSubButton>
                        </Sidebar.MenuSubItem>
                      {/each}
                    </Sidebar.MenuSub>
                  </div>
                {/if}
              {/snippet}
            </Collapsible.Content>
          </Sidebar.MenuItem>
        {/snippet}
      </Collapsible.Root>
    {/each}
  </Sidebar.Menu>
</Sidebar.Group>

<Sidebar.Group>
  <Sidebar.GroupLabel>Digital</Sidebar.GroupLabel>
  <Sidebar.Menu>
    {#each DigitalDashboard as item, idx (idx)}
      <Collapsible.Root class="group/collapsible" open={item.isActive}>
        {#snippet child({ props })}
          <Sidebar.MenuItem {...props}>
            <Collapsible.Trigger>
              {#snippet child({ props })}
                <Sidebar.MenuButton {...props}>
                  {#snippet tooltipContent()}
                    {item.title}
                  {/snippet}
                  <item.icon />
                  <span>{ item.title }</span> 
                  <ChevronRight class="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </Sidebar.MenuButton>
              {/snippet}
            </Collapsible.Trigger>
            <Collapsible.Content forceMount>
              {#snippet child({ props, open })}
                {#if open}
                  <div {...props} transition:slide>
                    <Sidebar.MenuSub>
                      {#each item.items as subItem, idx (idx)}
                        <Sidebar.MenuSubItem>
                          <Sidebar.MenuSubButton class={$page.url.pathname === subItem.page ? 'bg-sidebar-accent' : ''}>
                            {#snippet child({ props})}
                              <a href={subItem.page} {...props}>{ subItem.title }</a>
                            {/snippet}
                          </Sidebar.MenuSubButton>
                        </Sidebar.MenuSubItem>
                      {/each}
                    </Sidebar.MenuSub>
                  </div>
                {/if}
              {/snippet}
            </Collapsible.Content>
          </Sidebar.MenuItem>
        {/snippet}
      </Collapsible.Root>
    {/each}
  </Sidebar.Menu>
</Sidebar.Group>
  