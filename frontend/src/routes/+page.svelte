<script lang="ts">
    import * as Breadcrumb from "$lib/components/ui/breadcrumb/index.js";
	import { Separator } from "$lib/components/ui/separator/index.js";
    import * as Sidebar from "$lib/components/ui/sidebar/index.js";
    import * as Card from "$lib/components/ui/card";
    import { DollarSign, Users, CreditCard, Activity } from 'lucide-svelte';
</script>

<div class="flex flex-1 flex-col gap-4 p-4 pt-0">
    <div class="grid auto-rows-min gap-4 md:grid-cols-4">
        <Card.Root class="bg-primary text-primary-foreground">
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">
                    Total Revenue
                </Card.Title>
                <DollarSign class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
                <div class="text-2xl font-bold">
                    IDR 45,231.89 bn
                </div>
                <p class="text-sm text-muted-foreground">
                    +20.1% from last month
                </p>
            </Card.Content>
        </Card.Root>

        <Card.Root class="bg-primary text-primary-foreground">
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">
                    Active Subs
                </Card.Title>
                <Users class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
                <div class="text-2xl font-bold">
                    +2350
                </div>
                <p class="text-sm text-muted-foreground">
                    +180.1% from last month
                </p>
            </Card.Content>
        </Card.Root>

        <Card.Root class="bg-primary text-primary-foreground">
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">
                    Sales
                </Card.Title>
                <CreditCard class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
                <div class="text-2xl font-bold">
                    +12,234
                </div>
                <p class="text-sm text-muted-foreground">
                    +19% from last month
                </p>
            </Card.Content>
        </Card.Root>

        <Card.Root class="bg-primary text-primary-foreground">
            <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
                <Card.Title class="text-sm font-medium">
                    Active Now
                </Card.Title>
                <Activity class="h-4 w-4 text-muted-foreground" />
            </Card.Header>
            <Card.Content>
                <div class="text-2xl font-bold">
                    +573
                </div>
                <p class="text-sm text-muted-foreground">
                    +201 since last 24 hour
                </p>
            </Card.Content>
        </Card.Root>
    </div>
    <div class="bg-muted/50 min-h-[100vh] flex-1 rounded-xl md:min-h-min"></div>
</div>