<script lang="ts">
	import type { AppBreadcrumbItem } from '$lib/app-store.svelte';
    import * as Breadcrumb from '$lib/components/ui/breadcrumb';

    type Props = {
        data: AppBreadcrumbItem 
    }

    let { data }: Props = $props();

    const breadCrumbChain = $derived(makeBreadcrumb(data));

    function makeBreadcrumb(data: AppBreadcrumbItem): AppBreadcrumbItem[] {
        const chain: AppBreadcrumbItem[] = [];
        let current = data;
        chain.push(current);
        while (current.child) {
            current = current.child;
            chain.push(current);
        }
        
        console.log('making breadcrumb', chain);
        return chain;
    }

    function isLastCrumb(idx: number) {
        return idx + 1 < breadCrumbChain.length;
    }

</script>

<Breadcrumb.Root>
    <Breadcrumb.List>
        {#each breadCrumbChain as item, idx}
            <Breadcrumb.Item>
                {#if isLastCrumb(idx)}
                    <Breadcrumb.Link href={item.to}>{item.text}</Breadcrumb.Link>
                {:else}
                    <Breadcrumb.Page>{item.text}</Breadcrumb.Page>
                {/if}
            </Breadcrumb.Item>
            {#if isLastCrumb(idx)}
                <Breadcrumb.Separator />
            {/if}
        {/each}
    </Breadcrumb.List>
</Breadcrumb.Root>