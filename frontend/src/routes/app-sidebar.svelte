<script lang="ts" module>
	const data = {
		user: {
			name: "<PERSON><PERSON><PERSON>",
			email: "ziska.zark<PERSON><EMAIL>",
			avatar: "",
		},
	};
</script>

<script lang="ts">
	import NavMain from "./nav-main.svelte";
	import NavProjects from "$lib/components/nav-projects.svelte";
	import NavSecondary from "$lib/components/nav-secondary.svelte";
	import NavUser from "$lib/components/nav-user.svelte";
	import DashboardNav from './dashboard-nav.svelte';
	import * as Sidebar from "$lib/components/ui/sidebar/index.js";
	import Command from "lucide-svelte/icons/command";
	import type { ComponentProps } from "svelte";
	import EntitySelector from "./entity-selector.svelte";

	let { ref = $bindable(null), ...restProps }: ComponentProps<typeof Sidebar.Root> = $props();
</script>

<Sidebar.Root bind:ref variant="inset" collapsible="icon" {...restProps}>
	<Sidebar.Header>
		<Sidebar.Menu>
			<Sidebar.MenuItem>
				<Sidebar.MenuButton size="lg">
					{#snippet child({ props })}
						<EntitySelector/>
					{/snippet}
				</Sidebar.MenuButton>
			</Sidebar.MenuItem>
		</Sidebar.Menu>
	</Sidebar.Header>
	<Sidebar.Content>
		<DashboardNav />
	</Sidebar.Content>
	<Sidebar.Footer>
		<NavUser user={data.user} />
	</Sidebar.Footer>
</Sidebar.Root>
