<script lang="ts">
  import LoginForm from "./LoginForm.svelte";
	import RetroGrid from "$lib/components/ui/retro-grid/RetroGrid.svelte";
  import WavyBackground from "$lib/components/ui/wavy-background/WavyBackground.svelte";
  import { useSessionService } from '$lib/session';
  import { toast, type ExternalToast } from 'svelte-sonner';
	import GridBackground from "$lib/components/ui/grid-and-dot-background/GridBackground.svelte";

  const session = useSessionService();

  async function onlogin(user: string, pass: string) {
    try {
      const auth = await session.Authenticate(user, pass);
    } catch (e) {
      const loginError = (e as Error).message;
      toast.error('Login Error', {
        description: loginError,
        duration: 5000,
        position: 'top-center',
      })
    }
  }
</script>

<div class="relative flex h-screen w-full items-center justify-center px-4 overflow-hidden">
  <!-- <GridBackground> -->
    <LoginForm {onlogin}/>
    <WavyBackground 
      blur={2} 
      wave-opacity={0.5} colors={['#ED1C24', '#FFCB05', '#32BCAD', '#C6168D', '#EC008C']}
      wave-width={50}
      wave-num={5}
    />
  <!-- </GridBackground>   -->
</div>