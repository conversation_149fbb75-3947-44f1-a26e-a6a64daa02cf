<script lang="ts">
  import BorderBeam from '$lib/components/ui/border-beam/BorderBeam.svelte';
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { LoaderCircle } from 'lucide-svelte';
  import { IconBrandGoogle } from '@tabler/icons-svelte';
  import { useGoogleOAuthService } from '$lib/session/google-oauth.service';

  type Props = {
	onlogin: (user: string, pass: string) => Promise<void>;
  }

  let { onlogin }: Props = $props();

  let login = $state({ user: '', pass: '' });
  let is_submitting = $state(false);

  const gauth = useGoogleOAuthService();

  async function submitLogin(user: string, pass: string) {
	is_submitting = true;
	await onlogin(user, pass);
	is_submitting = false;
  }

  async function handleGoogleLogin() {
	gauth.handleLogin();
  }

</script>

<Card.Root class=" relative z-10 mx-auto max-w-sm bg-white/50 shadow-xl rounded-xl backdrop-blur-xl">
	<BorderBeam size={128} duration={7}/>
	<Card.Header>
		<Card.Title class="mx-auto text-2xl">Login</Card.Title>
	</Card.Header>
	<Card.Content>
		<form onsubmit={() => submitLogin(login.user, login.pass)}>

			<div class="grid gap-4">
				<div class="grid gap-2">
					<Label for="username">Username</Label>
					<Input id="username" type="text" placeholder="NIK" tabindex={1} required bind:value={login.user}/>
				</div>
				<div class="grid gap-2">
					<div class="flex items-center">
						<Label for="password">Password</Label>
						<a href="##" class="ml-auto inline-block text-sm underline" tabindex={3}>
							Forgot your password?
						</a>
					</div>
					<Input id="password" type="password" required tabindex={2} bind:value={login.pass}/>
				</div>
				<Button type="submit" class="w-full" disabled={is_submitting}>
					{#if is_submitting}
						<LoaderCircle class="animate-spin"/>
					{/if}
					Login
				</Button>
				<Button variant="outline" class="w-full" disabled={is_submitting} onclick={handleGoogleLogin}>
					<IconBrandGoogle class="h-5 w-5"/>
					Login using Google
				</Button>
			</div>
			<div class="mt-4 text-center text-sm">
				Don't have an account?
				<a href="##" class="underline"> Request access </a>
			</div>
		</form>
	</Card.Content>
</Card.Root>