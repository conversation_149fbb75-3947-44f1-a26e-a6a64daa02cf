<script lang="ts">
	import { useSessionService } from '$lib/session';
  import { onMount } from 'svelte';

  const sess = useSessionService();

  const hash = location.hash.substring(1);
  const params = new URLSearchParams(hash);
  
  onMount(() => {
    handleGoogleLogin();
  });

  async function handleGoogleLogin() {
    try {
      sess.AuthenticateWithGoogleOAuth(params);
    } catch (error) {
      
    }
  }

</script>