import {
  CircleDollarSign,
  HandCoins,
  Share2,
  Users,
} from 'lucide-svelte';

export const CoreDashboard = [
  {
    title: 'Revenue',
    icon: CircleDollarSign,
    isActive: true,
    items: [
      {
        title: 'Overview',
        page: '/core/revenue-overview',
      },
      {
        title: 'By Channel',
        page: '/core/revenue-by-channel',
      }
    ]
  },
  {
    title: 'Sales',
    icon: HandCoins,
    items: [
      {
        title: 'Overview',
        page: '/sales-overview',
      },
      {
        title: 'By Channel',
        page: '/sales-by-channel',
      }
    ]
  },
  {
    title: 'Distribution',
    icon: Share2,
    items: [
      {
        title: 'Overview',
        page: '/distribution-overview',
      },
      {
        title: 'By Channel',
        page: '/distribution-by-channel',
      }
    ]
  },
  {
    title: 'Subs',
    icon: Users,
    items: [
      {
        title: 'Overview',
        page: '/subs-overview',
      },
      {
        title: 'By Tenure',
        page: '/subs-by-tenure',
      }
    ]
  }
];

export const DigitalDashboard = [
  {
    title: 'Revenue',
    icon: CircleDollarSign,
    isActive: false,
    items: [
      {
        title: 'Overview',
        page: '/revenue-overview',
      },
      {
        title: 'By Channel',
        page: '/revenue-by-channel',
      }
    ]
  },
  {
    title: 'Sales',
    icon: HandCoins,
    items: [
      {
        title: 'Overview',
        page: '/sales-overview',
      },
      {
        title: 'By Channel',
        page: '/sales-by-channel',
      }
    ]
  },
  {
    title: 'Distribution',
    icon: Share2,
    items: [
      {
        title: 'Overview',
        page: '/distribution-overview',
      },
      {
        title: 'By Channel',
        page: '/distribution-by-channel',
      }
    ]
  },
  {
    title: 'Subs',
    icon: Users,
    items: [
      {
        title: 'Overview',
        page: '/subs-overview',
      },
      {
        title: 'By Tenure',
        page: '/subs-by-tenure',
      }
    ]
  }
];
