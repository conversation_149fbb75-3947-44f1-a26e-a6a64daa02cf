<script lang="ts">
	import '../app.css';
	import '../theme.css';
	import AppSidebar from "./app-sidebar.svelte";
  import AppHeader from './app-header.svelte';
  import { GridBackground } from '$lib/components/ui/grid-and-dot-background';
	
	import * as Sidebar from "$lib/components/ui/sidebar";
  import { Toaster } from '$lib/components/ui/sonner';
  import { useSessionStore, useSessionService } from '$lib/session';
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
  
	let { children } = $props();
  
  const sessStore = useSessionStore();
  
  $effect(() => {
    const authState = sessStore.authState;
    switch (authState) {
      case 'LoggedOut':
      case 'TokenExpired':
      case 'TokenInvalid':
        goto('/login');
        break;
      case 'LoggedIn':
        if (page.route.id?.startsWith('/login')) goto('/');
        break;
    }
  })
</script>

<Toaster toastOptions={{
	classes: {
		error: 'bg-red-700 text-white',
		success: 'text-green-600',
		warning: 'text-yellow-400',
		info: 'bg-blue-400'
	}
}}/>

<Sidebar.Provider>
  {#if sessStore.auth}
    <AppSidebar />
  {/if}
  <Sidebar.Inset>
    {#if sessStore.auth}
    <AppHeader />
    {/if}
    {@render children()}
  </Sidebar.Inset>
</Sidebar.Provider> 
