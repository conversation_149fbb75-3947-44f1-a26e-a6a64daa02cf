import { useApiService } from '$lib/api';
import { handleRouting } from '$lib/route';
import type { LayoutLoad } from './$types';
import { browser } from '$app/environment';

export const ssr = false;

let baseUrl = import.meta.env.VITE_API_BASE_URL;
if (import.meta.env.PROD && browser) {
    baseUrl = window.location.protocol + '//' + window.location.host + '/api';
}

useApiService(baseUrl);

export const load: LayoutLoad = async({ route }) => {
    if (route.id) handleRouting(route.id);
    return {};
}