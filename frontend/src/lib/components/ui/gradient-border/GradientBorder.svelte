<script lang="ts">
  import { cn } from "$lib/utils";
	import type { Snippet } from "svelte";

  type TColorProp = string | string[];

  type Props = {
    borderRadius?: number;
    borderWidth?: number;
    color?: TColorProp;
    class?: string;
    children: Snippet;
  };

  let { 
    borderRadius = 8, 
    borderWidth = 1, 
    color = ['#4FF9FF'], 
    class: className,
    children
  }: Props = $props();

</script>

<div
  style="
      --border-radius: {borderRadius}px;
    "
  class={cn(
    "relative min-h-[60px] min-w-[300px] place-items-center rounded-[var(--border-radius)] bg-white p-3 text-black dark:bg-black dark:text-white",
    className
  )}
>
  <div
    style="
        --border-width: {borderWidth}px;
        --border-radius: {borderRadius}px;
        --mask-linear-gradient: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        --background-radial-gradient: radial-gradient(transparent, transparent, {Array.isArray(
      color
    )
      ? color.join(',')
      : color});
      "
    class={cn(
      "before:absolute before:inset-0 before:aspect-square before:size-full before:rounded-[var(--border-radius)] before:p-[var(--border-width)] before:content-['']",
      "before:![-webkit-mask-composite:xor] before:![mask-composite:exclude] before:[background-image:var(--background-radial-gradient)] before:[background-size:300%_300%] before:[mask:var(--mask-linear-gradient)]",
      // "motion-safe:before:animate-[shine-pulse_var(--shine-pulse-duration)_infinite_linear]"
      )}
  ></div>
  {@render children()}
</div>