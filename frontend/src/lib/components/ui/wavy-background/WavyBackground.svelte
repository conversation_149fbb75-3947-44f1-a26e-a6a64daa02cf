<script lang="ts">
  import { cn } from '$lib/utils';
  import { createNoise3D } from 'simplex-noise';
  import { onMount, type Snippet } from 'svelte';

  type Props = {
    class?: string;
    'container-class-name'?: string;
    colors?: string[];
    'wave-width'?: number;
    'background-fill'?: string;
    blur?: number;
    speed?: 'slow' | 'fast';
    'wave-opacity'?: number;
    'wave-num'?: number;
    children?: Snippet;
  }

  let {
    class: className,
    'container-class-name': containerClassName,
    colors,
    'wave-width': waveWidth,
    'wave-num': waveNum = 5,
    'background-fill': backgroundFill,
    blur = 10,
    speed = 'fast',
    'wave-opacity': waveOpacity = 0.5,
    children,
    ...restProps
  }: Props = $props();

  const noise = createNoise3D();
  let w: number, h: number, nt: number, i: number, x: number, ctx: CanvasRenderingContext2D, canvas: HTMLCanvasElement;

  let canvasRef = $state<HTMLCanvasElement>();

  const getSpeed = () => {
    switch (speed) {
      case 'slow':
        return 0.001;
      case 'fast':
        return 0.002;
      default:
        return 0.001;
    }
  }

  const init = () => {
    canvas = canvasRef!;
    ctx = canvas.getContext('2d')!;
    w = ctx.canvas.width = window.innerWidth;
    h = ctx.canvas.height = window.innerHeight;
    ctx.filter = `blur(${blur}px)`;
    nt = 0;
    window.onresize = function() {
      w = ctx.canvas.width = window.innerWidth;
      h = ctx.canvas.height = window.innerHeight;
      ctx.filter = `blur(${blur}px)`;
    };
    render();

  }

  const waveColors = $derived(colors ?? ['#38bdf8', '#818cf8', '#c084fc', '#e879f9', '#22d3ee']);
  const drawWave = (n: number) => {
    nt += getSpeed();
    for (i = 0; i < n; i++) {
      ctx.beginPath();
      ctx.lineWidth = waveWidth || 50;
      ctx.strokeStyle = waveColors[i % waveColors.length];
      for (x = 0; x < w; x++) {
        const y = noise(x/800, 0.3 * i, nt) * 150;
        ctx.lineTo(x, y + h * 0.5);
      }

      ctx.stroke();
      ctx.closePath();
    }
  }

  let animationId: number;
  const render = () => {
    ctx.fillStyle = backgroundFill || 'white';
    ctx.globalAlpha = waveOpacity || 0.5;
    ctx.fillRect(0, 0, w, h);
    drawWave(waveNum);
    animationId = requestAnimationFrame(render);
  };

  onMount(() => {
    init();
    return () => {
      cancelAnimationFrame(animationId);
    }
  });

</script>

<div class={cn('flex h-screen flex-col items-center justify-center', containerClassName)}>
	<canvas class="absolute inset-0 z-0" bind:this={canvasRef} id="canvas"></canvas>
	<div class={cn('relative z-10', className)} {...restProps}>
		{@render children?.()}
	</div>
</div>