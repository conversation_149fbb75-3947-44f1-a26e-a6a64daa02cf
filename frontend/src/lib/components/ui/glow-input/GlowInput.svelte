<script lang="ts">
  import { cn } from '$lib/utils';
  import { useMotionTemplate, useMotionValue, Motion, MotionValue } from 'svelte-motion';

  type Props = {
    class?: string;
    type?: string;
  }

  let { class: className, type = 'text', ...restProps }: Props = $props();

  let visible = $state(false);

  let mouseX = useMotionValue(0);
  let mouseY = useMotionValue(0);

  function handleMouseMove({ currentTarget, clientX, clientY }: MouseEvent) {
    let { left, top } = (currentTarget as HTMLElement).getBoundingClientRect();
    mouseX.set(clientX - left);
    mouseY.set(clientY - top);
  }
</script>

<Motion let:motion>

</Motion>