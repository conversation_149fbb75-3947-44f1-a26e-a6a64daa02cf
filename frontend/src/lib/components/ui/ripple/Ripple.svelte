<script lang="ts">

  type Props = {
    'main-circle-size'?: number;
    'main-circle-opacity'?: number;
    'num-circles'?: number;
  };

  let {
    'main-circle-size': mainCircleSize = 210,
    'main-circle-opacity': mainCircleOpacity = 0.24,
    'num-circles': numCircles = 8,
  }: Props = $props();

</script>

<div
  class="z-0 absolute inset-0 flex items-center justify-center bg-white/5 [mask-image:linear-gradient(to_bottom,white,transparent)]"
>
  {#each { length: numCircles } as _, i}
    <div
      class="absolute animate-ripple rounded-full bg-foreground/30 shadow-xl border top-1/2 left-1/2 translate-x-1/2 translate-y-1/2"
      style="width: {mainCircleSize + i * 70}px;
        height: {mainCircleSize + i * 70}px;
        opacity: {mainCircleOpacity - i * 0.03}; 
         animation-delay: {i * 0.08}s;
         border-style:{i === numCircles - 1
        ? 'dashed'
        : 'solid'};
         border-width: 1px;
         border-color: rgba(var(--foreground-rgb), {(5 + i * 5) / 100});
         "
      ></div>
  {/each}
</div>