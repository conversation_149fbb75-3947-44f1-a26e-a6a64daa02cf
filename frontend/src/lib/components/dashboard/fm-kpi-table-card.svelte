<script lang="ts">
  import * as Card from "$lib/components/ui/card";
  import DataTable from "$lib/components/dashboard/data-table/data-table.svelte";
	import type { FmKpiValue } from "$lib/data/kpi/fullmonth";
	import { createFmMonthlyKpiTableColumns, createFmMonthlyKpi, type FmMonthlyKpi } from "./fm-kpi-table";
	import { cn } from "$lib/utils";
	import GradientBorder from "$lib/components/ui/gradient-border/GradientBorder.svelte";
  import { useAppStore } from '$lib/app-store.svelte'

  const app = useAppStore();

  type Props = {
    'is-loading': boolean;
    title: string;
    subtitle: string;
    data: FmKpiValue[];
    class?: string;
    'start-month': number;
    'end-month': number;
  }

  let { 'is-loading': isLoading, data, title, subtitle, class: className, 'start-month': startMonth, 'end-month': endMonth }: Props = $props();
  const gradientColors = $derived([app.ActiveEntityColor[0], app.ActiveEntityColor[app.ActiveEntityColor.length - 1]]);
  
  const columns = createFmMonthlyKpiTableColumns(startMonth, endMonth)
  let dataByKpi =$derived(createFmMonthlyKpi(data));

</script>

<GradientBorder color={gradientColors} borderRadius={12} class={cn("shadow overflow-hidden", className)}>
  <Card.Root class={cn("border-none shadow-none w-full")}>
    <Card.Header class="flex flex-row items-center">
      <div class="grid gap-2">
        <Card.Title>{title}</Card.Title>
        <Card.Description>
          {subtitle}
        </Card.Description>
      </div>
    </Card.Header>
    <Card.Content>
      <!-- {#if !isLoading} -->
      <DataTable columns={columns} data={dataByKpi} left-pin={['kpi_name', 'unit']}/>
      <!-- {/if} -->
    </Card.Content>
  </Card.Root>
</GradientBorder>
