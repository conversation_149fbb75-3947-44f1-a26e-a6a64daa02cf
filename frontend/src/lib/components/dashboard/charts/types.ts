import { type AxisConfigInterface, type CrosshairConfigInterface, type TooltipConfigInterface } from '@unovis/ts';

export interface ChartConfig<TData> {
    // x: NumericKeys<TData> | ((d: TData) => number | Date);
    // xAccessorFn?: (d: TData) => number | Date;
    // y: NumericKeys<TData>[] | ((d: TData) => number)[];
    // yAccessorFn?: ((d: TData) => number)[];
    // data?: TData[];
    data?: ChartSeries;
    xAxis?: AxisConfig;
    yAxis?: AxisConfig;
    crosshair?: CrosshairConfig<TData>;
    tooltip?: TooltipConfig<TData>;
    title?: string;
    width?: number;
    height?: number;
    colors?: string[];
    'is-loading'?: boolean;
    'chart-options'?: {
        [x: string]: any;
    };
    'disable-crosshair'?: boolean;
    'disable-tooltip'?: boolean;
}

export interface ChartData {
    x: number;
    [name: string]: number;
}

export interface Series {
    name: string;
    unit: string;
    data: [number, number][];
}

export interface ChartSeries {
    categories: number[];
    series: Series[];
}

export interface AxisConfig {
    label?: string;
    tickFormat?: ((tick: number | Date, i: number, ticks: number[] | Date[]) => string);
    tickLine?: boolean;
    tickFontSize?: number;
    tickTextColor?: string;
    tickTextAlign?: 'left' | 'right' | 'center';
    tickTextAngle?: number;
    tickTextWidth?: number;
    numTicks?: number;
    minMaxTicksOnly?: boolean;
    tickValues?: number[];
    gridLine?: boolean;
    domainLine?: boolean;
}

export interface CrosshairConfig<TData> extends CrosshairConfigInterface<TData> {
    color?: string[];
    // strokeColor?: string[];
    // strokeWidth?: string[];
    // template?: (d: TData) => string;
}

export interface TooltipConfig<TData> extends TooltipConfigInterface {
    // horizontalPlacement?: 'left' | 'right';
    // horizontalShift?: number;
    // verticalPlacement?: 'top' | 'bottom';
    // verticalShift?: number;
    // followCursor?: boolean;
    padding?: string;
    // triggers?: {
    //     [selector: string]: (
    //         datum: TData,
    //         i: number,
    //         els: Element[]
    //     ) => string | HTMLElement | null | void
    // };
    // container?: HTMLElement;
    dataLabelFormatter?: (d: number) => string;
}

export function AxisConfigToUnovis<TData>(cfg?: AxisConfig): AxisConfigInterface<TData> | undefined {
    if (cfg === undefined) return undefined;
    const tickFontSize = cfg.tickFontSize ? `${cfg.tickFontSize}px` : null;
    return {
        tickTextFontSize: tickFontSize,
        ...cfg
    }
}
