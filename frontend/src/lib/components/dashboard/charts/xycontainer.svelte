<script lang="ts">
  import { VisXY<PERSON><PERSON>r, VisAxis, VisCrosshair, VisBulletLegend, VisTooltip } from '@unovis/svelte5';
  import type { BulletLegendItemInterface, NumericAccessor, XYContainer } from '@unovis/ts';
	import { AxisConfigToUnovis, type ChartConfig, type ChartData } from './types';
  import { useAppStore } from '$lib/app-store.svelte';
  import { randomId } from '$lib/utils/generator';
	import { createChartDataFromChartSeries } from './util';
  import { convertToRGBA } from '$lib/utils/colors';

  const app = useAppStore();
  const { 
    data, 
    xAxis,
    yAxis, 
    chart: Child, 
    'chart-options': chartOptions,
    'disable-crosshair': disableCrosshair = false,
    'disable-tooltip': disableTooltip = false,
    'is-loading': isLoading = false,
    crosshair,
    tooltip,
    ...rest}: ChartConfig<ChartData> & {chart: any} = $props();
  
  const chartData = $derived(data ? createChartDataFromChartSeries(data) : []);
  // $inspect(chartData);
  let x: NumericAccessor<ChartData> = (d: ChartData) => d.x;

  // let y = $state<NumericAccessor<Datum>[]>(data?.series.map(s => (d: ChartData) => d[s.name]) ?? []);
  let y = $state<NumericAccessor<ChartData>[]>(data?.series.map(s => (d: ChartData) => d[s.name]) ?? []);
  let container: VisXYContainer<ChartData>;
  
  let chartColors = $state<string[]>((rest.colors ?? app.ActiveEntityColor).map(col => convertToRGBA(col, 1)));

  function updateYVisibility(i: number, isvisible: boolean) {
    const tempY = [...y];
    tempY[i] = isvisible ? (d: ChartData) => d[data!.series[i].name]: 0;
    y = tempY;
  }

  let items = $state<BulletLegendItemInterface[]>([]);

  $effect(() => {
    const series = data?.series ?? [];
    if (series.length < 2) { items = []; return; }; 
    items = series.map((s, i) => {
      let color: string | undefined = undefined;
      if (app.ActiveEntityColor.length > i) color = app.ActiveEntityColor[i];
      return { name: s.name, color: color, inactive: false};
    });

  })

  $effect(() => {
    app.ActiveEntityColor;
    chartColors = (rest.colors ?? app.ActiveEntityColor).map(col => convertToRGBA(col, 1));
  })

  function toggleItem(item: BulletLegendItemInterface, i: number) {
    const inactive = !items[i].inactive;
    items = items.map((item, index) => {
      if (index === i) {
        return { ...item, inactive: !item.inactive };
      }
      return item;
    });
    updateYVisibility(i, !inactive);
    const color = (rest.colors ?? app.ActiveEntityColor).map((col) => convertToRGBA(col, 1));
    chartColors = color;
    container.getComponent().updateComponents([{x, y, color}]);
  }

  function itemHover(item: BulletLegendItemInterface, i: number) {
    if (item.inactive) return;
    const colors = (rest.colors ?? app.ActiveEntityColor).map((col, index) => {
      // if (index === i) return convertToRGBA(col, 1);
      if (index === i) return convertToRGBA('hsl(var(--primary))', 1);
      return convertToRGBA(col, 0.1);
    });
    chartColors = colors;
    container.getComponent().updateComponents([{x, y, color: colors}]);
  }

  function itemUnhover(item: BulletLegendItemInterface, i: number) {
    const colors = (rest.colors ?? app.ActiveEntityColor).map(col => convertToRGBA(col, 1));
    chartColors = colors;
    container.getComponent().updateComponents([{x, y, color: colors}]);
  }

  const crosshairColor = $derived.by(() => {
    if (crosshair?.color) return (d: ChartData, i: number) => crosshair.color ? crosshair.color[i] : app.ActiveEntityColor[i];
    return app.ActiveEntityColor;
  });

  const xAxisConf = $derived(AxisConfigToUnovis(xAxis));
  const yAxisConf = $derived(AxisConfigToUnovis(yAxis));

  const chartID = randomId();

</script>

{#key chartData}
<VisXYContainer data={chartData} width={rest.width} height={rest.height} bind:this={container}>
  {#if !isLoading}
    <Child {x} {y} color={(d: ChartData, i:number) => chartColors[i]} {...chartOptions}/>
  {:else}
    <div>Loading...</div>
  {/if}
  <VisAxis type="x" {...xAxisConf}/>
  {#if yAxis}
    <VisAxis type="y" {...yAxisConf}/>
  {/if}
  {#if !disableCrosshair}
    <VisCrosshair color={crosshairColor} {...crosshair}/>
  {/if}
  {#if !disableTooltip}
    <VisTooltip {...tooltip} />
  {/if}
</VisXYContainer>
<VisBulletLegend {items} onLegendItemClick={toggleItem} onLegendItemMouseEnter={itemHover} onLegendItemMouseLeave={itemUnhover}/>
{/key}
