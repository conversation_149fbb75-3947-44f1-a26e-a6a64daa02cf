<script lang="ts">
  import { VisStackedBar } from '@unovis/svelte5';
  import XYContainer from './xycontainer.svelte';
	import { type ChartConfig, type ChartData } from './types';
  import { useAppStore } from '$lib/app-store.svelte';
	import { formatNumberDecimal } from '$lib/utils/formatter';
	import { untrack } from 'svelte';

  const app = useAppStore();
  let {tooltip: propTooltip, crosshair: propCrosshair, ...props}: ChartConfig<ChartData> = $props();
  
  let crosshair = $derived.by(() => {
    let barCrosshair = propCrosshair;
    const data = untrack(() => props.data);
    let tickFormat = (tick: number | Date, i: number, ticks: number[] | Date[]) => `${tick}`
    if (props.xAxis && props.xAxis.tickFormat) {
      tickFormat = props.xAxis.tickFormat;
    }

    if (!barCrosshair) barCrosshair = {};
    let defaultFormatter = (d: number) => `${formatNumberDecimal(d)}`

    const template = (d: ChartData) => {
      if (!data) return '';
      const category = d.x;
      const valuePart = data.series.map(y => {
        const value = d[y.name];
        if (data.series.length > 1) {
          return `<p><span class="font-medium">${y.name}</span>: ${defaultFormatter(value)}</p>`
        }
        return `<p>${value}</p>`
      }).join('')

      return `<div class="font-medium text-sm">${tickFormat(category, 0, [])}</div><div class="text-sm">${valuePart}</div>`
    }

    if (!barCrosshair.template) barCrosshair.template = template;

    return barCrosshair;
  });

  let tooltip = $derived.by(() => {
    
    return propTooltip;
  });

</script>

{#if props.data}
<XYContainer {tooltip} {crosshair} {...props} chart={VisStackedBar} />
{/if}
