import type { NumericKeys } from '$lib/utils/type';
import type { ChartData, ChartSeries, Series } from './types';

export function createGenericChartSeries<T>(
    data: T[], 
    xAccessor: NumericKeys<T> | ((data: T) => number), 
    yAccessor: (NumericKeys<T>)[] | ((data: T) => number)[],
    seriesNames: string | string[],
    unit?: string,
): ChartSeries {
    let genericSeriesNameCount = 0
    const series: Series[] = [];

    yAccessor.forEach((ac, idx) => {
        let name: string;
        if (seriesNames.length >= idx + 1) {
            name = seriesNames[idx];
        } else {
            genericSeriesNameCount++;
            name = `series ${genericSeriesNameCount}`
        }

        const seriesData: [number, number][] = data.map(d => [getAccessorValue(d, xAccessor), getAccessorValue(d, ac)]);
        seriesData.sort((a, b) => a[0] - b[0])
        series.push({ name: name, unit: unit ?? '', data: seriesData });
    })

    const categories = data.map(d => getAccessorValue(d, xAccessor)).sort((a, b) => a - b);
    return {
        categories,
        series
    }
}

function getAccessorValue<T>(d: T, accessor: NumericKeys<T> | ((data: T) => number)): number {
    switch (typeof accessor) {
        case 'string':
            return d[accessor] as number
            break;
        case 'function':
            return accessor(d);
            break;
        default:
            return 0
            break;
    }
}

export function createChartDataFromChartSeries(series: ChartSeries): ChartData[] {
    const datums: ChartData[] = [];
    const categories = series.categories;
    categories.forEach((cat, i) => {
        const datum: ChartData = {
            x: cat
        };
        series.series.forEach(s => {
            const val = (s.data.find(x => x[0] === cat)?.[1]) ?? 0
            datum[s.name] = val;
        })

        datums.push(datum);
    });
    return datums;
}
