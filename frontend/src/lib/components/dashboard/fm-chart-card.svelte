<script lang="ts">
  import * as Card from "$lib/components/ui/card";
  import Spinner from '$lib/components/spinner.svelte';
  import { formatDateToMonthYear } from '$lib/utils/formatter';
  import * as Chart from "$lib/components/dashboard/charts";
	import type { FmKpiValue } from "$lib/data/kpi/fullmonth";
	import { createGenericChartSeries } from "./charts/util";
	import { randomId } from "$lib/utils/generator";
  
  let { 
    icon: Icon,
    'is-loading': isLoading = false,
    'kpi-name': kpiName,
    data,
    'series-name': seriesName,
  }: {
    data?: FmKpiValue[],
    'kpi-name': string,
    icon?: any,
    'is-loading'?: boolean,
    'series-name'?: string,
  } = $props();

  const id = randomId();

  const chartSeries = $derived(createGenericChartSeries(data ?? [], 'month_date', [(d: FmKpiValue) => d.value ?? 0], seriesName ?? ''))

</script>

<div {id} class="primary">
  <Card.Root class="bg-primary text-primary-foreground drop-shadow-md" >
    <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
      <Card.Title class="text-sm font-medium">
        { kpiName }
      </Card.Title>
      <Icon class="h-4 w-4 text-muted-primary"/>
    </Card.Header>
    <Card.Content>
      <div class="primary">
        {#if !isLoading}
          <Chart.Bar
            chart-options={{barPadding: 0.2}}
            data={chartSeries}
            xAxis={{
              tickFormat: (d: number | Date) => formatDateToMonthYear(d), 
              tickLine: false,
              tickTextColor: 'hsl(var(--primary-foreground))',
              gridLine: false,
              minMaxTicksOnly: true,
            }}
            yAxis={{gridLine: false, minMaxTicksOnly: true, domainLine: false, tickTextColor: 'hsl(var(--primary-foreground))'}}
            height={85}
            colors={['hsl(var(--primary-foreground))']}
            crosshair={{color: ['hsl(var(--secondary-foreground))']}}
            tooltip={{padding: '3px 3px', verticalPlacement: 'top', container: document.getElementById(id) ?? undefined}}
          />
        {:else}
          <Spinner class="text-center"/>
        {/if}
      </div>
    </Card.Content>
  </Card.Root>
</div>

<style>
  .primary {
    --vis-axis-tick-color: hsl(var(--primary-foreground));
    --vis-tooltip-padding: 3px 3px;
    --vis-tooltip-background-color: rgba(230, 214, 188, 0.9);
  }
</style>