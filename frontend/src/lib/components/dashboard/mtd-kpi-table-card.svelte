<script lang="ts">
  import * as Card from "$lib/components/ui/card";
  import * as Table from "$lib/components/ui/table";
  import { Button } from '$lib/components/ui/button';
  import { ArrowUpRight } from 'lucide-svelte';
  import type { MtdKpiValue } from "$lib/data/kpi/mtd";
  import { formatNumberDecimal, RoundFloat } from "$lib/utils/formatter";
  import Spinner from "../spinner.svelte";

  type Props = {
    'is-loading': boolean;
    title: string;
    subtitle: string;
    data: MtdKpiValue[];
  }

  let { 'is-loading': isLoading, data, title, subtitle }: Props = $props();

</script>

<Card.Root>
  <Card.Header class="flex flex-row items-center">
    <div class="grid gap-2">
      <Card.Title>{title}</Card.Title>
      <Card.Description>
        {subtitle}
      </Card.Description>
    </div>
    <Button size="sm" class="ml-auto gap-1">
      View All
      <ArrowUpRight class="h-4 w-4" />
    </Button>
  </Card.Header>
  <Card.Content>
    {#if !isLoading}
    <div>
      <Table.Root>
        <Table.Header>
          <Table.Row>
            <Table.Head>Circle</Table.Head>
            <Table.Head class="text-right">
              LMTD
            </Table.Head>
            <Table.Head class="text-right">
              MTD
            </Table.Head>
            <Table.Head class="text-right">
              Growth
            </Table.Head>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {#each data as kpi}
          <Table.Row>
            <Table.Cell>
              <div class="font-medium">{kpi.entity_name}</div>
            </Table.Cell>
            <Table.Cell class="text-right">
              {formatNumberDecimal(kpi.lmtd_value)}
            </Table.Cell>
            <Table.Cell class="text-right">
              {formatNumberDecimal(kpi.mtd_value)}
            </Table.Cell>
            <Table.Cell class="text-right">
              {kpi.growth_text}
            </Table.Cell>
          </Table.Row>
          {/each}
        </Table.Body>
        <Table.Footer>
          <Table.Row>
            {@const lmtdsum = data.reduce((acc, curr) => acc + (curr.lmtd_value ?? 0), 0)}
            {@const mtdsum = data.reduce((acc, curr) => acc + (curr.mtd_value ?? 0), 0)}
            {@const growthsum = (mtdsum - lmtdsum) / lmtdsum * 100}
            <Table.Cell class="font-medium">Total</Table.Cell>
            <Table.Cell class="text-right">
              {formatNumberDecimal(lmtdsum)}
            </Table.Cell>
            <Table.Cell class="text-right">
              {formatNumberDecimal(mtdsum)}
            </Table.Cell>
            <Table.Cell class="text-right">
              {formatNumberDecimal(RoundFloat(growthsum, 1), 'en-US', true)}%
            </Table.Cell>
          </Table.Row>
        </Table.Footer>
      </Table.Root>
    </div>
    {:else}
    <Spinner class="text-primary"/>
    {/if}
  </Card.Content>
</Card.Root>