<script lang="ts">
  import * as Card from '$lib/components/ui/card';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu';
  import { Button } from '$lib/components/ui/button';
  import { Menu } from 'lucide-svelte';
	import type { Snippet } from 'svelte';
  import { randomId } from '$lib/utils/generator';
  import { toPng, toSvg } from 'html-to-image';

  type CardChartProps = {
    title: string | Snippet;
    children: Snippet;

  }

  const chartId = `data-chart-${randomId()}`;

  let { title, children }: CardChartProps = $props();

  function downloadSvg() {
    const node = document.getElementById(chartId);
    if (!node) return;
    toSvg(node)
      .then(function (dataUrl: string) {
        const link = document.createElement('a');
        link.download = `${chartId}.svg`;
        link.href = dataUrl;
        link.click();
    })
    .catch(function (error: unknown) {
        console.error('oops, something went wrong!', error);
    });
  }

  function downloadPng() {
    const node = document.getElementById(chartId);
    if (!node) return;
    const { width, height } = getNodeSize(node);
    toPng(node, {canvasHeight: height * 2, canvasWidth: width * 2})
      .then(function (dataUrl: string) {
        const link = document.createElement('a');
        link.download = `${chartId}.png`;
        link.href = dataUrl;
        link.click();
    })
    .catch(function (error: unknown) {
        console.error('oops, something went wrong!', error);
    });
  }

function getNodeSize(node: HTMLElement) {
  // Get the element's dimensions including padding and border
  const boundingRect = node.getBoundingClientRect();
  const dimensions = {
    width: boundingRect.width,
    height: boundingRect.height,
    
    // If you need more detailed measurements:
    clientWidth: node.clientWidth,    // width without borders
    clientHeight: node.clientHeight,  // height without borders
    offsetWidth: node.offsetWidth,    // width including padding and border
    offsetHeight: node.offsetHeight,  // height including padding and border
    scrollWidth: node.scrollWidth,    // entire content width including overflow
    scrollHeight: node.scrollHeight   // entire content height including overflow
  };
  
  return dimensions;
}

</script>

<div id={chartId}>
  <Card.Root class="relative overflow-hidden">
    <Card.Header>
      <Card.Title class="relative">
        {#if typeof title === 'string'}
          {title}
        {:else}
          {@render title()}
        {/if}
        <DropdownMenu.Root>
          <DropdownMenu.Trigger class="absolute right-3 top-2 z-10">
            <Button variant="default" class="h-4 w-5"><Menu size="1em"/></Button>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content>
            <DropdownMenu.Item onclick={downloadPng}>Download PNG</DropdownMenu.Item>
            <DropdownMenu.Item onclick={downloadSvg}>Download SVG</DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu.Root>
      </Card.Title>
    </Card.Header>
    <Card.Content>
      {@render children()}
    </Card.Content>
  </Card.Root>
</div>
