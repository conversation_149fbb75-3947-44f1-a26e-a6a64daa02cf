<script lang="ts" generics="TData, TValue">
  import { type ColumnDef, getCoreRowModel, type ColumnPinningState, type Table as tTable, type Column, type ColumnMeta } from '@tanstack/table-core';
  import { createSvelteTable, FlexRender } from '$lib/components/ui/data-table' 
  import * as Table from '$lib/components/ui/table';

  type DataTableProps<TData, TValue> = {
    columns: ColumnDef<TData, TValue>[];
    data: TData[];
    'left-pin'?: (keyof TData)[];
    'right-pin'?: (keyof TData)[];
    onrowclick?: (row: TData) => void;
    'is-loading'?: boolean;
  };

  let { data, columns, onrowclick = ()=>{}, 'left-pin': leftPin, 'right-pin': rightPin }: DataTableProps<TData, TValue> = $props();
  
  const colPin: ColumnPinningState = {};
  if (leftPin) {
    colPin.left = leftPin.map(val => val.toString());
  }

  if (rightPin) {
    colPin.right = rightPin.map(val => val.toString());
  }

  const table = createSvelteTable({
    get data() {
      return data;
    },
    columns,
    getCoreRowModel: getCoreRowModel(),
    initialState: {
      columnPinning: colPin
    },
  });

  interface PinID {
    id: string;
    pin_style: string;
  }

  function getColumnPinningStyles(column: Column<TData>) {
    const isPinned = column.getIsPinned();
    let style = '';
    if (isPinned) {
      style += 'position: sticky;';
      style += 'width:' + column.getSize() + ';';
      style += 'z-index: 1;';
      if (isPinned === 'left') {
        style += 'left:' + column.getStart('left') + 'px;';
      } else if (isPinned === 'right') {
        style += 'right:' + column.getAfter('right') + 'px;';
      }
      return style;
    }
    return '';
  }
  
</script>

<Table.Root>
  <Table.Header>
    {#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
      <Table.Row>
        {#each headerGroup.headers as header(header.id)}
          <Table.Head style={getColumnPinningStyles(header.column)} class="border-t bg-accent">
            {#if !header.isPlaceholder}
              <FlexRender
                content={header.column.columnDef.header}
                context={header.getContext()}
              />
            {/if}
          </Table.Head>
        {/each}
      </Table.Row>
    {/each}
  </Table.Header>

  <Table.Body>
    {#each table.getRowModel().rows as row (row.id)}
      <Table.Row data-state={row.getIsSelected() && "selected" } onclick={() => onrowclick(row.original)}>
        {#each row.getVisibleCells() as cell (cell.id)}
          {@const cellColor = cell.column.columnDef.meta?.getColor?.(cell.getContext()) ?? 'white'}
          <Table.Cell style={getColumnPinningStyles(cell.column) + ` background-color: ${cellColor}`}>
            <FlexRender
              content={cell.column.columnDef.cell}
              context={cell.getContext()}
            />
          </Table.Cell>
        {/each}
      </Table.Row>
    {:else}
      <Table.Row>
        <Table.Cell colspan={columns.length}>
          No Results.
        </Table.Cell>
      </Table.Row>
    {/each}
  </Table.Body>
  <Table.Footer>
    {#each table.getFooterGroups() as footerGroup (footerGroup.id)}
      <Table.Row>
        {#each footerGroup.headers as footer (footer.id)}
          <Table.Cell style={getColumnPinningStyles(footer.column)} class="bg-accent">
            <FlexRender
              content={footer.column.columnDef.footer}
              context={footer.getContext()}
            />
          </Table.Cell>
        {/each}
      </Table.Row>
    {/each}
  </Table.Footer>
</Table.Root>