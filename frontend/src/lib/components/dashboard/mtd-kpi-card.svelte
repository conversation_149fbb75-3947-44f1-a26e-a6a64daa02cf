<script lang="ts">
  import * as Card from "$lib/components/ui/card";
  import * as Carousel from "$lib/components/ui/carousel";
  import Autoplay from 'embla-carousel-autoplay';
  import { Badge } from "$lib/components/ui/badge";
  import Spinner from '$lib/components/spinner.svelte';
	import { formatDate } from '$lib/utils/formatter';
  import { TrendingDown, TrendingUp, TrendingUpIcon} from 'lucide-svelte';
  
  let { 
    'mtd-text': mtdText,
    'mtd-date': mtdDate,
    'growth-text': growthText, 
    icon: Icon,
    'is-loading': isLoading = false,
    'kpi-name': kpiName,
    'lmtd-text': lmtdText,
    'lmtd-date': lmtdDate,
  }: {
    'mtd-text': string,
    'mtd-date'?: number,
    'growth-text'?: string, 
    'kpi-name': string,
    icon?: any,
    'is-loading'?: boolean,
    'lmtd-text'?: string,
    'lmtd-date'?: number,
  } = $props();

  const isDown = $derived(growthText?.startsWith('-') ?? false);
  const TrendIcon = $derived(isDown ? TrendingDown : TrendingUp);
  const trendColor = $derived(isDown ? 'text-red-900' : 'text-green-700');
</script>

<Card.Root class="bg-primary text-primary-foreground drop-shadow-md">
<!-- <Card.Root class="border-primary border"> -->
  <Card.Header class="flex flex-row items-center justify-between space-y-0 pb-2">
    <Card.Title class="text-sm font-medium">
      { kpiName }
    </Card.Title>
    <Icon class="h-4 w-4 text-muted-primary"/>
  </Card.Header>
  <Card.Content>
    {#if !isLoading}
      <Badge variant="secondary">{mtdDate ? `as of ${formatDate(mtdDate, 'DD MMM YYYY')}` : '' }</Badge>
      <div class="text-2xl font-bold">
        <TrendIcon class={`h-6 w-6 inline ${trendColor} drop-shadow-sm`}/> {growthText} <span class="hidden xl:inline text-base">from last month</span>
      </div>
      <Carousel.Root plugins={[Autoplay({delay: 5000, stopOnInteraction: false})]}>
        <Carousel.Content>
          <Carousel.Item>
            <p class="text-sm text-muted-primary">
              { `MTD: ${mtdText}` }
            </p>
          </Carousel.Item>
          <Carousel.Item>
            <p class="text-sm text-muted-primary">
              { `LMTD: ${lmtdText}` }
            </p>
          </Carousel.Item>
        </Carousel.Content>
      </Carousel.Root>
    {:else}
      <Spinner class="text-center"/>
    {/if}
  </Card.Content>
</Card.Root>