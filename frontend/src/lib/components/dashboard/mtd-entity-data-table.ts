import type { MtdKpiValue } from '$lib/data/kpi/mtd';
import { formatNumberDecimal, RoundFloat } from '$lib/utils/formatter';
import type { ColumnDef } from '@tanstack/table-core';
import { createRawSnippet } from 'svelte';
import { renderSnippet } from '../ui/data-table';


export function createMtdKpiDataTableColumns(entity_title: string, is_link_entity: boolean = false): ColumnDef<MtdKpiValue>[] {
    let entity_class = "font-medium";
    if (is_link_entity) {
        entity_class += " underline cursor-pointer";
    }
    
    return [
    {
        accessorKey: 'entity_name',
        header: entity_title,
        footer: 'Total',
        cell: (props) => {
            const entitySnippet = createRawSnippet<[string]>((getEntity) => {
                const entity = getEntity();
                return {
                    render: () => `<div class="${entity_class}">${entity}</div>`
                };
            });

            return renderSnippet(entitySnippet, props.getValue() as string);
        },
        size: 100
    },
    {
        id: 'mtd_value',
        accessorFn: (row) => formatNumberDecimal(row.mtd_value),
        header: 'MTD',
        footer: (rows) => {
            const mtdsum = rows.table.getCoreRowModel().rows.reduce((acc, row) => acc + (row.original.mtd_value ?? 0), 0)
            return formatNumberDecimal(mtdsum)
        },
        size: 50
    },
    {
        id: 'lmtd_value',
        accessorFn: (row) => formatNumberDecimal(row.lmtd_value),
        header: 'LMTD',
        footer: (rows) => {
            const lmtdsum = rows.table.getCoreRowModel().rows.reduce((acc, row) => acc + (row.original.lmtd_value ?? 0), 0)
            return formatNumberDecimal(lmtdsum)
        }
    },
    {
        id: 'growth',
        accessorFn: (row) => row.growth_text,
        header: 'Growth',
        footer: (rows) => {
            const mtdsum = rows.table.getCoreRowModel().rows.reduce((acc, row) => acc + (row.original.mtd_value ?? 0), 0)
            const lmtdsum = rows.table.getCoreRowModel().rows.reduce((acc, row) => acc + (row.original.lmtd_value ?? 0), 0)
            const growthrate = (mtdsum - lmtdsum) / lmtdsum
            const growth_text = formatNumberDecimal(RoundFloat(growthrate * 100, 1), 'en-US', true) + '%'
            const isNegative = growthrate < 0;
            const className = 'font-medium' + isNegative ? 'text-red-500' : 'text-green-500';

            const growthSnippet = createRawSnippet<[string]>((getGrowth) => {
                const growth = getGrowth();
                return {
                    render: () => `<div class="${className}">${growth_text}</div>`
                };
            });

            return renderSnippet(growthSnippet, growth_text);
        },
        cell: (props) => {
            const growthValue = props.row.original.growth ?? 0;
            const isNegative = growthValue < 0;
            const className = isNegative ? 'text-red-500' : 'text-green-500';
            const growthSnippet = createRawSnippet<[string]>((getGrowth) => {
                const growth = getGrowth();
                return {
                    render: () => `<div class="${className}">${growth}</div>`
                };
            });

            return renderSnippet(growthSnippet, props.getValue() as string);
        }
    }
  ]
}