<script lang="ts">
  import * as Card from "$lib/components/ui/card";
  import DataTable from "$lib/components/dashboard/data-table/data-table.svelte";
  import { Button } from '$lib/components/ui/button';
  import { ArrowUpRight } from 'lucide-svelte';
  import type { MtdKpiValue } from "$lib/data/kpi/mtd";
  import Spinner from "../spinner.svelte";
	import { createMtdKpiDataTableColumns } from "./mtd-entity-data-table";

  type Props = {
    'is-loading': boolean;
    title: string;
    subtitle: string;
    'entity-title': string;
    data: MtdKpiValue[];
    onrowclick?: (row: MtdKpiValue) => void;
  }

  let { 'is-loading': isLoading, data, title, subtitle, 'entity-title': entityTitle, onrowclick = ()=>{} }: Props = $props();

  const columns = createMtdKpiDataTableColumns(entityTitle, true);

</script>

<Card.Root>
  <Card.Header class="flex flex-row items-center">
    <div class="grid gap-2">
      <Card.Title>{title}</Card.Title>
      <Card.Description>
        {subtitle}
      </Card.Description>
    </div>
    <Button size="sm" class="ml-auto gap-1">
      View All
      <ArrowUpRight class="h-4 w-4" />
    </Button>
  </Card.Header>
  <Card.Content>
    {#if !isLoading}
    <DataTable columns={columns} data={data} {onrowclick} left-pin={['entity_name', 'mtd_value']}/>
    {:else}
    <Spinner class="text-primary"/>
    {/if}
  </Card.Content>
</Card.Root>