import { formatDateToMonthYear, formatNumberDecimal, RoundFloat } from '$lib/utils/formatter';
import type { CellContext, ColumnDef, ColumnMeta } from '@tanstack/table-core';
import { createRawSnippet } from 'svelte';
import { renderSnippet } from '../ui/data-table';
import * as date from '@internationalized/date';
import type { FmKpiValue } from '$lib/data/kpi/fullmonth';
import { calculatePercentiles, getHeatmapColorFromPercentiles } from '$lib/utils/stats';

export interface FmMonthlyKpi {
    kpi_name: string;
    unit: string;
    [key: number]: FmKpiValue;
}

export function createFmMonthlyKpi(kpis: FmKpiValue[]): FmMonthlyKpi[] {
    const kpiMap = new Map<string, FmMonthlyKpi>();
    const kpiList: FmMonthlyKpi[] = [];

    kpis.forEach((kpi) => {
        const { kpi_name, unit, ...rest } = kpi;
        if (!kpiMap.has(kpi_name)) {
            const newKpi = {
                kpi_name,
                unit,
            };
            kpiMap.set(kpi_name, newKpi);
            kpiList.push(newKpi);
        }

        const kpiItem = kpiMap.get(kpi_name);
        if (kpiItem) {
            const month_date = date.fromDate(new Date(rest.month_date), date.getLocalTimeZone());
            const month_key = month_date.year * 100 + month_date.month;
            kpiItem[month_key] = {kpi_name, unit, ...rest};
        }
    });

    return kpiList;
}


export function createFmMonthlyKpiTableColumns(start_month: number, end_month: number): ColumnDef<FmMonthlyKpi>[] {
    let kpi_class = "font-medium";
    const start_date = date.fromDate(new Date(start_month), date.getLocalTimeZone());
    const end_date = date.fromDate(new Date(end_month), date.getLocalTimeZone());
    
    const yearDiff = end_date.year - start_date.year;
    const monthDiff = end_date.month - start_date.month;
    const totalMonths = yearDiff * 12 + monthDiff + 1;


    const month_list: date.ZonedDateTime[] = [];
    for (let i = 0; i < totalMonths; i++) {
        const month = start_date.add({ months: i });
        month_list.push(month);
    }

    const monthColumnDefs: ColumnDef<FmMonthlyKpi>[] = month_list.map((month) => {
        const month_id = `${month.year}${month.month}`;
        const month_int = month.year * 100 + month.month;
        return {
            id: month_id,
            accessorFn: (row) => row[month_int]?.value ?? 0,
            header: formatDateToMonthYear(month.toDate()),
            size: 150,
            cell: (props) => {
                const valueSnippet = createRawSnippet<[number]>((getValue) => {
                    return {
                        render: () => `<div>${formatNumberDecimal(getValue())}</div>`
                    };
                });

                return renderSnippet(valueSnippet, props.getValue() as number);
            },
            meta: {
                getColor: (props) => {
                    const rowValues = month_list.map((month) => {
                        const month_int = month.year * 100 + month.month;
                        return props.row.original[month_int]?.value ?? 0;
                    });

                    const stats = calculatePercentiles(rowValues);
                    const value = props.getValue() as number;

                    return getHeatmapColorFromPercentiles(value, stats);
                }
            }
        };
    });

    return [
    {
        id: 'kpi_name',
        accessorKey: 'kpi_name',
        header: 'KPI',
        cell: (props) => {
            const entitySnippet = createRawSnippet<[string]>((getKPI) => {
                const kpi = getKPI();
                return {
                    render: () => `<div class="${kpi_class}">${kpi}</div>`
                };
            });
            return renderSnippet(entitySnippet, props.getValue() as string);
        },
        size: 70,
    },
    {
        id: 'unit',
        accessorKey: 'unit',
        header: 'Unit',
        size: 50
    },
    ...monthColumnDefs,
  ]

}