<script lang="ts">
import { Loader } from 'lucide-svelte';
import { cn } from '$lib/utils';

let { class: className }: {class?: string} = $props();
</script>

<!-- <style>
  .animate-spin {
    animation: spin 3s linear;
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style> -->

<!-- <Loader className="animate-spin" class={cn(className, "h-6 w-6 text-muted-primary animate-spin")} /> -->
<Loader class="h-6 w-6 text-muted-primary animate-spin" />