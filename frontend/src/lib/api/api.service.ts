import { HttpClient, WithFormData } from '../utils/http';
import type { HttpResponse, BaseResponse, RequestOption, ProgressCallFn } from '../utils/http';

// export const APIRoutes = [
//   //auth
//   '/auth/authenticate',
//   '/auth/validate',
//   '/auth/refresh',

//   //Report
//   '/report/:report_id',

// ] as const

// export type APIEndpoint = typeof APIRoutes[number];

export type APIRoute = 
  //auth
  '/auth/authenticate' | 
  '/auth/validate' | 
  '/auth/refresh' | 
  
  // Google OAuth
  '/google-oauth/callback' |
  
  // Report
  '/report/:report_id';

export enum APIError {
  BADREQUEST = 'Bad Request',
  FORBIDDEN = 'Forbidden',
  UNAUTHORIZED = 'Unauthorized',
  INTERNAL_ERROR = 'Internal Server Error',
}

interface requestOptions {
  headers: Map<string, string>;
  params?: URLSearchParams;
  form?: FormData;
}

export class ApiService {
  private token: string | null = null;

  private client: HttpClient;
  private securedClient: HttpClient;

  constructor(private baseUrl: string) {
    this.client = new HttpClient();
    this.securedClient = new HttpClient({ inject: this.securedRequestInjector });
  }

  CreateAPIUrl(route: APIRoute, params?: URLSearchParams): URL {
    let path = <string>route;
    const removedParam: string[] = [];
    if (params) {
      params.forEach((val: any, key: string) => {
        if (path.indexOf(':' + key) > 0) {
          path = path.replace(':' + key, val);
          removedParam.push(key);
        }
      });
    }

    if (removedParam.length > 0) {
      removedParam.forEach(val => {
        params?.delete(val);
      })
    }

    const url = new URL(this.baseUrl + path)
    if (params) {
      url.search = params.toString();
    }
    return url;
  }

  setToken(token: string) {
    this.token = token;
  }

  removeToken() {
    this.token = null;
  }

  setOptions(...options: RequestOption[]): requestOptions {
    const op = <requestOptions>{ headers: new Map<string, string>() };
    options.forEach(val => {
      val(op);
    });

    return op;
  }

  async Get<T>(route: APIRoute, secured: boolean, ...options: RequestOption[]): Promise<T> {
    return this.BaseGet<T>(route, secured, ...options)
      .then(res => {
        if (res.code >= 400) {
          return Promise.reject(new Error(res.error ?? 'unknown error'))
        }
        return res.data;
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async BaseGet<T>(route: APIRoute, secured: boolean, ...options: RequestOption[]): Promise<BaseResponse<T>> {
    const opt = this.setOptions(...options)
    const url = this.CreateAPIUrl(route, opt.params)
    let client = new HttpClient();
    if (secured) {
      client = this.GetSecuredAPIClient();
    }

    return client.Get<BaseResponse<T>>(url, ...options)
      .then(res => {
        if (res.ok) {
          if (res.payload) {
            return res.payload;
          }

          return Promise.reject('no payload');
        }
        return Promise.reject(this.generateError(res));
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async Post<T>(route: APIRoute, secured: boolean, payload: object, ...options: RequestOption[]): Promise<T> {
    return this.BasePost<T>(route, secured, payload, ...options)
      .then(res => {
        if (res.code >= 400) {
          return Promise.reject(new Error(res.error ?? 'unknown error'))
        }
        return res.data;
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async BasePost<T>(route: APIRoute, secured: boolean, payload: object, ...options: RequestOption[]): Promise<BaseResponse<T>> {
    const opt = this.setOptions(...options);
    const url = this.CreateAPIUrl(route, opt.params);
    let client = this.client;
    if (secured) {
      client = this.securedClient;
    }

    return client.Post<BaseResponse<T>>(url, JSON.stringify(payload), ...options)
      .then(res => {
        if (res.ok) {
          if (res.payload) {
            return res.payload;
          }
          return Promise.reject('no payload');
        }
        return Promise.reject(this.generateError(res));
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async Delete<T>(route: APIRoute, secured: boolean, ...options: RequestOption[]): Promise<T> {
    const opt = this.setOptions(...options)
    const url = this.CreateAPIUrl(route, opt.params)
    let client = new HttpClient();
    if (secured) {
      client = this.GetSecuredAPIClient();
    }

    return client.Delete<BaseResponse<T>>(url, ...options)
      .then(res => {
        if (res.ok) {
          if (res.payload) {
            return res.payload.data;
          }

          return Promise.reject('no payload');
        }
        return Promise.reject(this.generateError(res));
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async RawPost(route: APIRoute, secured: boolean, payload: object, ...options: RequestOption[]): Promise<Response> {
    const opt = this.setOptions(...options);
    const url = this.CreateAPIUrl(route, opt.params);
    let client = this.client;
    if (secured) {
      client = this.securedClient;
    }

    return client.Raw("POST", url, JSON.stringify(payload), ...options)
      .then(async (res) => {
        if (res.ok) {
          return Promise.resolve(res)
        }

        return Promise.reject(await res.json())
      })
  }

  async Put<T>(route: APIRoute, secured: boolean, payload: object, ...options: RequestOption[]): Promise<T> {
    const opt = this.setOptions(...options);
    const url = this.CreateAPIUrl(route, opt.params)

    let client = new HttpClient();
    if (secured) {
      client = this.GetSecuredAPIClient();
    }

    return client.Put<BaseResponse<T>>(url, JSON.stringify(payload), ...options)
      .then(res => {
        if (res.ok && res.payload) {
          const payload = res.payload;
          if (payload.code >= 400) {
            return Promise.reject(new Error(payload.error ?? 'unknown error'))
          }
          return res.payload.data;
        }
        return Promise.reject(new Error(res.statusText));
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async Upload<T>(route: APIRoute, secured: boolean, f: File, ...options: RequestOption[]): Promise<T> {
    return this.BaseUpload<T>(route, secured, f, ...options)
      .then(res => {
        if (res.code >= 400) {
          return Promise.reject(new Error(res.error ?? 'unknown error'))
        }

        return res.data;
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  async UploadWithProgress<T>(route: APIRoute, secured: boolean, f: File, progressCall: (ev: ProgressEvent<XMLHttpRequestEventTarget>) => void, resFn: (resBody: string) => void, ...options: RequestOption[]): Promise<void> {
    const opt = this.setOptions(...options);
    const url = this.CreateAPIUrl(route, opt.params)

    if (opt.params) {
      opt.params.forEach((val: string, key: string) => {
        options.push(WithFormData(key, val));
      })
    }

    let client = this.client;
    if (secured) {
      client = this.securedClient;
    }

    return client.UploadFileWithProgress(url, f, progressCall, resFn, ...options);
  }

  async UploadFormWithProgress(route: APIRoute, secured: boolean, data: FormData, progressCall: ProgressCallFn, resFn: (res: string) => void, ...options: RequestOption[]): Promise<void> {
    const opt = this.setOptions(...options);
    const url = this.CreateAPIUrl(route, opt.params)

    if (opt.params) {
      opt.params.forEach((val: string, key: string) => {
        options.push(WithFormData(key, val));
      })
    }

    let client = this.client;
    if (secured) {
      client = this.securedClient;
    }

    return client.UploadFormWithProgress(url, data, progressCall, resFn, ...options);
  }

  async BaseUpload<T>(route: APIRoute, secured: boolean, f: File, ...options: RequestOption[]): Promise<BaseResponse<T>> {
    const opt = this.setOptions(...options);
    const url = this.CreateAPIUrl(route, opt.params)

    if (opt.params) {
      opt.params.forEach((val: string, key: string) => {
        options.push(WithFormData(key, val));
      })
    }

    let client = this.client;
    if (secured) {
      client = this.securedClient;
    }

    return client.UploadFile<BaseResponse<T>>(url, f, ...options)
      .then(res => {
        if (res.ok) {
          if (res.payload) {
            return res.payload;
          }

          return Promise.reject('no payload');
        }
        return Promise.reject(this.generateError(res));
      })
      .catch((err: Error) => {
        return Promise.reject(err);
      })
  }

  // async Send<T>(request: Request, secured: boolean): Promise<BaseResponse<T>> {
  //     let client = this.client
  //     if (secured) client = this.securedClient;
  // }

  GetSecuredAPIClient(): HttpClient {
    const client = new HttpClient({ inject: this.securedRequestInjector });
    return client;
  }

  GetSecuredHeader(): Headers {
    const token = this.token;
    const headers = new Headers();
    if (token) {
      headers.set('Authorization', 'Bearer ' + token);
    }

    return headers;
  }

  private securedRequestInjector = (request: Request): Request => {
    const token = this.token;
    const newReq = new Request(request, { credentials: 'include' });
    if (token) {
      newReq.headers.set('Authorization', 'Bearer ' + token);
    }

    newReq.headers.set('ngrok-skip-browser-warning', '69420');
    return newReq;
  };

  private generateError<T>(res: HttpResponse<BaseResponse<T>>): Error {
    const err = new Error();
    if (res.payload?.error) {
      err.message = res.payload?.error;
    }

    switch (res.status) {
      case 400:
        err.name = APIError.BADREQUEST;
        break;
      case 401:
        err.name = APIError.UNAUTHORIZED;
        break;
      case 403:
        err.name = APIError.FORBIDDEN;
        break;
      case 500:
        err.name = APIError.INTERNAL_ERROR;
        break;
      default:
        err.name = APIError.INTERNAL_ERROR;
        break;
    }

    return err;
  }
}

let apiService: ApiService;

export function useApiService(baseUrl?: string): ApiService {
  if (!apiService) {
    if (!baseUrl) {
      throw new Error('baseUrl required to create new ApiService');
    }

    apiService = new ApiService(baseUrl);
  }

  return apiService;
}
