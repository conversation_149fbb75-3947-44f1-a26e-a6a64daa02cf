<script lang="ts" generics="Datum">
  import { BulletLegend, type BulletLegendConfigInterface, type BulletLegendItemInterface } from '@unovis/ts';
  import { onMount, untrack } from 'svelte';

  import { arePropsEqual } from '../../utils/props';

  let { items, ...restProps }: BulletLegendConfigInterface = $props();

  // config
  let prevConfig: BulletLegendConfigInterface | undefined = undefined;
  let config = $derived<BulletLegendConfigInterface>({ items, ...restProps});

  let component = $state<BulletLegend>();
  let ref = $state<HTMLDivElement>();
  
  // $inspect(config, prevConfig);
  onMount(() => {
    component = new BulletLegend(ref!, { ...config, renderIntoProvidedDomNode: true});
    return () => component?.destroy();
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component?.update(config)
      prevConfig = config
    }
  });

  // component accessor
  export function getComponent(): BulletLegend { 
    return component!
  }

</script>

<vis-bullet-legend bind:this={ref}></vis-bullet-legend>

<style>
  vis-bullet-legend {
    display:block;
  }
</style>