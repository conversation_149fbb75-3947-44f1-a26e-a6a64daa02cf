<script lang="ts" generics="Datum">
	import type { Lifecycle } from '$lib/unovis/types/context';
	import { arePropsEqual } from '$lib/unovis/utils/props';
  import { Axis, type AxisConfigInterface } from '@unovis/ts';
  import { onMount, getContext } from 'svelte';

  interface Props extends AxisConfigInterface<Datum> {
    data?: Datum[];
  }

  let { data, ...restProps}: Props = $props();

  let prevConfig: AxisConfigInterface<Datum> | undefined = undefined;
  let config = $derived<AxisConfigInterface<Datum>>({...restProps});

  let component = $state<Axis<Datum>>();
  const lifecycle = getContext<Lifecycle>('axis');
  
  onMount(() => {
    component = new Axis<Datum>(config);
    return () => component?.destroy();
  });

  $effect(() => {
    if (data) component?.setData(data);
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component?.setConfig(config)
      prevConfig = config
    }
  });

  // component accessor
  export function getComponent(): Axis<Datum> { 
    return component!
  }

</script>

{#if component}
<vis-component use:lifecycle={component!}></vis-component>
{/if}