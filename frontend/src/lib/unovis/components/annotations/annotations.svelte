<script lang="ts" generics="Datum">
  import { Annotations, type AnnotationsConfigInterface, type AnnotationItem } from '@unovis/ts'
  import { onMount, getContext } from 'svelte';

  import type { Lifecycle } from '../../types/context';
  import { arePropsEqual } from '../../utils/props';

  let { items, ...restProps }: AnnotationsConfigInterface = $props();

  // config
  let prevConfig = $state<AnnotationsConfigInterface>();
  let config = $derived<AnnotationsConfigInterface>({ items, ...restProps});

  let component = $state<Annotations>();
  const lifecycle = getContext<Lifecycle>('annotations');
  
  onMount(() => {
    component = new Annotations(config);
    return () => component?.destroy();
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component?.setConfig(config)
      prevConfig = config
    }
  });

  // component accessor
  export function getComponent(): Annotations { 
    return component!
  }

</script>

{#if component}
  <vis-component use:lifecycle={component!}></vis-component>
{/if}