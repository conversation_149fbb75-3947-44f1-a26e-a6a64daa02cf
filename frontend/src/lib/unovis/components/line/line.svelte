<script lang="ts" generics="Datum">
  import { Line, type LineConfigInterface, type NumericAccessor } from '@unovis/ts'
  import { onMount, getContext } from 'svelte';

  import type { Lifecycle } from '../../types/context';
  import { arePropsEqual } from '../../utils/props';

  interface Props extends LineConfigInterface<Datum> {
    data?: Datum[];
  }

  let { data, x, y, ...restProps }: Props = $props();

  let prevConfig: LineConfigInterface<Datum> | undefined = undefined;
  const config = $derived<LineConfigInterface<Datum>>({x, y, ...restProps});
  let component = $state<Line<Datum>>();

  const lifecycle = getContext<Lifecycle>('component');
  
  onMount(() => {
    component = new Line<Datum>(config);
    return () => component?.destroy();
  });

  $effect(() => {
    if (data) component?.setData(data);
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component!.setConfig(config);
      prevConfig = config;
    }
  });

  // component accessor
  export function getComponent(): Line<Datum> { 
    return component!
  }

</script>

{#if component}
<vis-component use:lifecycle={component!}></vis-component>
{/if}