<script lang="ts" generics="Datum">
	import type { Lifecycle } from '$lib/unovis/types/context';
	import { arePropsEqual } from '$lib/unovis/utils/props';
  import { Crosshair, type CrosshairConfigInterface } from '@unovis/ts';
  import { onMount, getContext } from 'svelte';

  interface Props extends CrosshairConfigInterface<Datum> {
    data?: Datum[];
  }

  let { x, y, data, ...restProps}: Props = $props();

  let prevConfig: CrosshairConfigInterface<Datum> | undefined = undefined;
  let config = $derived<CrosshairConfigInterface<Datum>>({x, y, ...restProps});

  let component = $state<Crosshair<Datum>>();
  const lifecycle = getContext<Lifecycle>('crosshair');
  
  onMount(() => {
    component = new Crosshair<Datum>(config);
    return () => component?.destroy();
  });

  $effect(() => {
    if (data) component?.setData(data);
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component?.setConfig(config)
      prevConfig = config
    }
  });

  // component accessor
  export function getComponent(): Crosshair<Datum> { 
    return component!
  }

</script>

{#if component}
<vis-component use:lifecycle={component!}></vis-component>
{/if}