<script lang="ts" generics="Datum">
  import { Tooltip, type TooltipConfigInterface } from '@unovis/ts'
  import { onMount, getContext } from 'svelte';

  import type { Lifecycle } from '../../types/context';
  import { arePropsEqual } from '../../utils/props';

  let { ...restProps }: TooltipConfigInterface = $props();

  // config
  let prevConfig: TooltipConfigInterface | undefined = undefined;
  let config = $derived<TooltipConfigInterface>({...restProps});

  let component = $state<Tooltip>();
  const lifecycle = getContext<Lifecycle>('tooltip');
  
  onMount(() => {
    component = new Tooltip(config);
    return () => component?.destroy();
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component?.setConfig(config)
      prevConfig = config
    }
  });

  // component accessor
  export function getComponent(): Tooltip { 
    return component!
  }

</script>

{#if component}
<vis-component use:lifecycle={component!}></vis-component>
{/if}
