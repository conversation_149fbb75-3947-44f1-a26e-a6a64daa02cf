<script lang="ts" generics="Datum">
  import { StackedBar, type StackedBarConfigInterface } from '@unovis/ts'
  import { onMount, getContext } from 'svelte';

  import type { Lifecycle } from '../../types/context';
  import { arePropsEqual } from '../../utils/props';

  interface Props extends StackedBarConfigInterface<Datum> {
    data?: Datum[];
  }

  let { data, x, y, ...restProps }: Props = $props();

  let prevConfig: StackedBarConfigInterface<Datum> | undefined = undefined;
  const config = $derived<StackedBarConfigInterface<Datum>>({x, y, ...restProps});
  let component = $state<StackedBar<Datum>>();

  const lifecycle = getContext<Lifecycle>('component');
  
  onMount(() => {
    component = new StackedBar<Datum>(config);
    return () => component?.destroy();
  });

  $effect(() => {
    if (data) component?.setData(data);
  });

  $effect(() => {
    if (!arePropsEqual(prevConfig, config)) {
      component!.setConfig(config);
      prevConfig = config;
    }
  });

  // component accessor
  export function getComponent(): StackedBar<Datum> { 
    return component!
  }

</script>

{#if component}
<vis-component use:lifecycle={component!}></vis-component>
{/if}