<script lang="ts" generics="Datum">
  import { <PERSON>Y<PERSON><PERSON><PERSON>, XYComponentCore, type XYContainerConfigInterface, Tooltip, Crosshair, Axis, Annotations, type NumericAccessor } from '@unovis/ts';
	import { onMount, type Snippet, setContext, untrack } from 'svelte';
  import { type NumericKeys } from '$lib/utils/type';

  interface Props extends XYContainerConfigInterface<Datum> {
    data?: Datum[];
    class?: string;
    children: Snippet;
  }

  let { data, class: className, children, ...restProps}: Props = $props();

  let chart = $state<XYContainer<Datum>>();

  const config: XYContainerConfigInterface<Datum> = {
    components: [],
    crosshair: undefined,
    tooltip: undefined,
    xAxis: undefined,
    yAxis: undefined,
    annotations: undefined,
  };

  let ref: HTMLDivElement;
  
  $effect(() => {
    if (data) chart?.setData(data, true);
  });

  let animationFrame = $state(0);
  const updateContainer = async () => {
    // due to the order of events when a component is removed update container can be called
    // while a component is being destroyed. This can lead to an error because we trigger an update
    // with a destroyed component.
		config.components = config.components?.filter(e => !e.isDestroyed());
		// we can't use animation frames in a non-browser environment
		if (typeof requestAnimationFrame === 'undefined') {
        chart?.updateContainer({
          ...config,
          ...restProps
        });
			return;
		}

    const myAnimation = untrack(() => animationFrame);

		if (myAnimation) {
			cancelAnimationFrame(myAnimation);
		}

		// this prevent multiple renders from happening in a single frame
		// when a component is first rendered the components will be pushed 1 by 1
		// so we don't want to rerender every time a component is added
		animationFrame = requestAnimationFrame(() => {chart?.updateContainer({
        ...config,
        ...restProps
      });
      animationFrame = 0;
		});
	};

  $effect(() => {
    data;
    updateContainer();
  });

  onMount(() => {
    chart = new XYContainer(ref!, config, data)
    return () => chart?.destroy()
  });

  setContext('component', (e: HTMLElement, c: XYComponentCore<Datum>) => {
    $effect(() => {
      if (!c) return;
      if (config.components?.includes(c)) return;
      config.components = [...config.components ?? [], c];

      return () => {
        config.components = config.components?.filter(c => !c.isDestroyed()) ?? [];
      }
    })
  })

  setContext('axis', (e: HTMLElement & { __type__: 'x' | 'y'}, c: Axis<Datum>) => {
    $effect(() => {
      if (config[`${e.__type__}Axis`] === c) return;
      e.__type__ = c.config.type as 'x' | 'y';
      config[`${e.__type__}Axis`] = c;

      return () => {
        config[`${e.__type__}Axis`] = undefined;
      };
    });
  });

  setContext('crosshair', (e: HTMLElement, c: Crosshair<Datum>) => {
    $effect(() => {
      if (config.crosshair === c) return;
      config.crosshair = c;

      return () => {
        config.crosshair = undefined;
      };
    });
  });

  setContext('tooltip', (e: HTMLElement, c: Tooltip) => {
    $effect(() => {
      if (config.tooltip === c) return;
      config.tooltip = c;

      return () => {
        config.tooltip = undefined;
      };
    });
  });

  setContext('annotations', (e: HTMLElement, c: Annotations) => {
    $effect(() => {
      if (config.annotations === c) return;
      config.annotations = c;

      return () => {
        config.annotations = undefined;
      };
    });
  });

  export function getComponent(): XYContainer<Datum> {
    return chart!;
  }

</script>

<vis-xy-container bind:this={ref} class={`unovis-xy-container ${className}`}>
  {@render children()}
</vis-xy-container>


<style>
  .unovis-xy-container {
    display: block;
    position: relative;
    width: 100%;
  }
</style>