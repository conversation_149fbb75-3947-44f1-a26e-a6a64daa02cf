import { goto } from '$app/navigation';
import { useSessionService } from '$lib/session';

const unprotected_routes = [
    "/login",
    "/login/google-oauth",
    "/register",
    "/forgot-password",
    "/reset-password",
    "/verify-email",
];

export async function handleRouting(path: string) {
    console.log('handling route:', path);
    const session = useSessionService();
    if (unprotected_routes.includes(path)) {
        return;
    }

    console.log('checking session');
    if (!await session.CheckSession()) {
        console.log('session invalid');
        goto("/login");
    }
}