import IOHLogo from '$lib/components/ioh-logo.svelte';
import IM3Logo from '$lib/components/im3-logo.svelte';
import ThreeIDLogo from '$lib/components/3id-logo.svelte';
import type { Entity, EntityNames } from '$lib/report';
import { Entities } from '$lib/report';
import type { HeatmapColors } from './utils/stats';

export class AppBreadcrumbItem {
  public child?: AppBreadcrumbItem

  constructor(
    public text: string,
    public to: string,
  ) {}

  setChild(child: AppBreadcrumbItem) {
    this.child = child
  }

  addChild(text: string, to: string) {
    if (this.child) {
      this.child.addChild(text, to)
      return
    }

    this.child = new AppBreadcrumbItem(text, to)
  }

  removeChild() {
    this.child = undefined;
  }
}

export type EntityIcon = typeof IOHLogo | typeof IM3Logo | typeof ThreeIDLogo

export const EntityIcons = new Map<
  EntityNames,
  typeof IOHLogo | typeof IM3Logo | typeof ThreeIDLogo
>([
  ['IOH', IOHLogo],
  ['IM3', IM3Logo],
  ['3ID', ThreeIDLogo],
])

export const EntityColors: { [key in EntityNames]: Array<string> } = {
  // 'IOH': ['#c81c46', '#ad2a6d', '#833f83', '#554a83', '#334c72', '#2f4858'],
  'IOH': ['#a50f15', '#de2d26', '#fb6a4a', '#fcae91', '#fee5d9'],
  'IM3': ['#432600', '#674c00', '#947400', '#c69f00', '#facc15'],
  '3ID': ['#0041c0', '#2563eb', '#567eff', '#7b99ff', '#9eb6ff'],
}

export function getEntityIconByName(name: EntityNames) {
  return EntityIcons.get(name)
}

function getEntityByName(name: string): Entity {
  const selected = Entities.find((entity) => entity.name === name)
  return selected || Entities[0]
}

const AppStore = $state({
    SidebarIsOpen: true,
    ActiveEntity: getEntityByName('IOH'),
    ActiveEntityColor: EntityColors['IOH'],
    CurrentBreadcrumb: new AppBreadcrumbItem('Summary', '/summary'),
    setActiveEntity(name: string) {
      this.ActiveEntity = getEntityByName(name)
      this.ActiveEntityColor = EntityColors[name as EntityNames]
    },
    toggleSideBar(open?: boolean) {
      if (!open) {
        open = !this.SidebarIsOpen
      }
      this.SidebarIsOpen = open;
    }
})

export function useAppStore() {
  return AppStore;
}
