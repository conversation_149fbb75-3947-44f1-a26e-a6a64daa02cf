import type { FmMonthlyKpi } from '$lib/components/dashboard/fm-kpi-table';
import type { MtdKpiValue } from '$lib/data/kpi/mtd';
import { type EntityNames, type KpiDTO, type ReportID } from './base.dto';
import type { CoreKpiDTO } from './core.dto';

// type ReportCacheItem<CData> = {
//     'IOH': CData[];
//     'IM3': CData[];
//     '3ID': CData[];
// }

type ReportCacheItem<CData> = {
    [key in EntityNames]?: CData[];
}

type ReportCache = {
    'comm_dash001': {
        'monthly_national_kpi'?: ReportCacheItem<KpiDTO<CoreKpiDTO>>,
        'monthly_circle_kpi'?: ReportCacheItem<KpiDTO<CoreKpiDTO>>,
        'mtd_national_kpi'?: ReportCacheItem<KpiDTO<CoreKpiDTO>>,
        'mtd_circle_kpi'?: ReportCacheItem<KpiDTO<CoreKpiDTO>>,
        'mtd_region_kpi'?: ReportCacheItem<KpiDTO<CoreKpiDTO>>,
    }
}

// type ReportCache = {
//     'comm_dash001': {
//         'monthly_national_kpi'?: {[key in EntityNames]?: KpiDTO<CoreKpiDTO>[]},
//         'monthly_circle_kpi'?: { [key in EntityNames] ?: KpiDTO < CoreKpiDTO > [] },
//         'mtd_national_kpi'?: { [key in EntityNames]?: KpiDTO<CoreKpiDTO>[] },
//         'mtd_circle_kpi'?: { [key in EntityNames]?: KpiDTO<CoreKpiDTO>[] },
//         'mtd_region_kpi'?: { [key in EntityNames]?: KpiDTO<CoreKpiDTO>[] },
//     }
// }

// type ReportCache = {
//     [key in keyof ReportCacheItem]: {
//         [ent in keyof ReportCacheItem[key]]: ReportCacheItem[key][ent];
//     }
// };

const ReportStore = {
    Cache: {
        'comm_dash001': {},
    } as ReportCache,

    setCacheItem<
        TRep extends ReportID, 
        TCache extends keyof ReportCache[TRep], 
        TEntity extends keyof NonNullable<ReportCache[TRep][TCache]>,
    >(
        report_id: TRep, 
        entity: TEntity, 
        cache_name: TCache, 
        data: NonNullable<ReportCache[TRep][TCache]>[TEntity]
    ) {
        if (!this.Cache[report_id][cache_name]) {
            this.Cache[report_id][cache_name] = {} as ReportCache[TRep][TCache];
        }

        this.Cache[report_id][cache_name]![entity] = data;
    },

    getCacheItem<
        TRep extends ReportID, 
        TCache extends keyof ReportCache[TRep],
        TEnt extends keyof NonNullable<ReportCache[TRep][TCache]>
    >(
        report_id: TRep, 
        entity: TEnt, 
        cache_name: TCache
    ): NonNullable<ReportCache[TRep][TCache]>[TEnt] | undefined 
    {
        if (!this.Cache[report_id][cache_name]) {
            return undefined;
        }

        return this.Cache[report_id][cache_name]![entity];
    }
}

export function useReportStore() {
    return ReportStore;
}
