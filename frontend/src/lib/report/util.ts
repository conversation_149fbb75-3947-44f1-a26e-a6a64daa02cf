import * as date from '@internationalized/date';

export function getMtd(): {mtd: date.CalendarDate, lmtd: date.CalendarDate} {
    const today = date.today(date.getLocalTimeZone());
    let mtd = today.subtract({days: 5});
    const lmtd = mtd.subtract({months: 1});

    if (mtd.day > lmtd.day) {
        mtd = mtd.subtract({days: mtd.day - lmtd.day});
    }

    return {mtd, lmtd};
}

export function formatToMonthID(day: date.CalendarDate): string {
    const fmter = new date.DateFormatter('en-CA', { year: 'numeric', month: '2-digit' });
    return fmter.format(day.toDate(date.getLocalTimeZone())).replace('-', '');
}
