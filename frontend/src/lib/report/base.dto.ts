export const Entities = [
    { name: 'IOH', desc: 'Indosat Ooredoo Hutchison' },
    { name: 'IM3', desc: 'Indosat' },
    { name: '3ID', desc: 'Three Indonesia' },
] as const

export type Entity = (typeof Entities)[number]
export type EntityNames = (typeof Entities)[number]['name']

export const Groupings = [
    'National',
    'Circle',
    'Region'
] as const

export type Grouping = typeof Groupings[number];

export type TimeInstance = 'Daily' | 'FullMonth' | 'MTD' | 'YTD';

export const BusinessUnits = [
    'Core',
    'Digital',
    'Fintech'
] as const
export type BusinessUnit = typeof BusinessUnits[number];

export type ReportID =
    'comm_dash001'

export interface ReportRequestDTO {
    report_id: ReportID;
    entity: EntityNames;
    grouping_id: Grouping;
    t_instance_id: TimeInstance;
    start_time?: string;
    end_time: string;
    bu_id: BusinessUnit;
}

export interface IdentifierDTO {
    grouping: Grouping;
    entity: string;
    circle?: string;
    region?: string;
}

export interface TimeInstanceDTO {
    type: TimeInstance;
    name: string;
    value: number;
}

export interface KpiDTO<T> {
    time_instance: TimeInstanceDTO;
    identifier: IdentifierDTO;
    kpi: T;
}
