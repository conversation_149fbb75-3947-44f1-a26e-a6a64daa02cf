export const CircleNames = [
    'JAKARTA RAYA',
    'JAVA',
    'SUMATERA',
    'KALISUMAPA'
] as const;

export type Circle = typeof CircleNames[number]

export const RegionMap = {
    'JAKARTA RAYA': [
        'WEST JAVA',
        'INNER JAKARTA',
        'OUTER JAKARTA'
    ] as const,
    'JAVA': [
        'BALI NUSRA',
        'CENTRAL JAVA',
        'EAST JAVA',
    ] as const,
    'KALISUMAPA': [
        'MAPA',
        'SULAWESI',
        'KALIMANTAN'
    ] as const,
    'SUMATERA': [
        'NORTH SUMATERA',
        'SOUTH SUMATERA',
        'CENTRAL SUMATERA'
    ] as const
}

export type Region<C extends Circle> = typeof RegionMap[C][number]
