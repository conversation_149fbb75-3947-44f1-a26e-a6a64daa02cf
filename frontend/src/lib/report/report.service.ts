import { useApiService } from "$lib/api";
import { WithParams, type BaseResponse } from "$lib/utils/http";
import type { KpiDTO, ReportRequestDTO } from "./base.dto";
import type { CoreKpiDTO } from "./core.dto";


export class ReportService {
    async getReport(req: ReportRequestDTO): Promise<KpiDTO<CoreKpiDTO>[]> {
        const api = useApiService();
        const params = new URLSearchParams({
            report_id: req.report_id,
            entity: req.entity,
            grouping_id: req.grouping_id,
            t_instance_id: req.t_instance_id,
            end_time: req.end_time,
        });

        if (req.start_time) params.set('start_time', req.start_time);
        
        return api.Get<KpiDTO<CoreKpiDTO>[]>('/report/:report_id', true, WithParams(params))
    }
}

let reportService: ReportService;

export function useReportService() {
    if (!reportService) {
        reportService = new ReportService();
    }

    return reportService;
}

