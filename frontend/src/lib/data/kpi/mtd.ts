import { formatDateToMonthYear } from "$lib/utils/formatter";
import type { KpiValue } from "./base";
import { type ValueConfig, KpiUtil } from "./util";

export interface MtdKpiItem extends KpiValue {
    mtd_name?: string;
    month_name?: string;
    mtd_date?: number;
}

export interface MtdKpiValue {
    kpi_name: string;
    unit: string;
    entity_name?: string;
    mtd_value?: number;
    mtd_value_text?: string;
    mtd_date?: number;
    lmtd_value?: number;
    lmtd_value_text?: string;
    lmtd_date?: number;
    growth?: number;
    growth_text?: string;
}

function createMtdKpiItem(kpi_value: KpiValue, mtd_name: string, mtd_date: number): MtdKpiItem {
    return {
        mtd_name,
        mtd_date,
        month_name: formatDateToMonthYear(mtd_date),
        ...kpi_value
    };
}

export const MtdUtil = {
    createMtdKpiItem
}