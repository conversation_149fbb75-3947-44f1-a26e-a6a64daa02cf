export interface CoreKpi {
    rev_total?: number;
    traffic_gb?: number;
    vlr_daily?: number;
    rgs_ga?: number;
    rgu_30?: number;
    rgu_30_gross_churn?: number;
    rgu_30_churn_back?: number;
    rgu_90?: number;
    rgu_90_gross_churn?: number;
    rgu_90_churn_back?: number;
    qsso?: number;
    sso?: number;
    quro?: number;
    uro?: number;
    secondary_trad?: number;
    tertiary_trad?: number;
}

export type CoreKpiName = keyof CoreKpi;

export const CoreKpiAlias: {[key in keyof CoreKpi]: string} = {
    rev_total: 'Revenue',
    traffic_gb: 'Traffic',
    vlr_daily: 'VLR',
    rgs_ga: 'Gross Add',
    rgu_30: 'RGU 30D',
    rgu_30_gross_churn: 'RGU30 Gross Churn',
    rgu_30_churn_back: 'RGU30 Churn Back',
    rgu_90: 'RGU 90D',
    rgu_90_gross_churn: 'RGU90 Gross Churn',
    rgu_90_churn_back: 'RGU90 Churn Back',
    qsso: 'QSSO',
    sso: 'SSO',
    quro: 'QURO',
    uro: 'uro',
    secondary_trad: 'Secondary',
    tertiary_trad: 'Tertiary',
}
