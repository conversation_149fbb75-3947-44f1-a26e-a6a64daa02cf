import { RoundFloat, formatMetric } from '$lib/utils/formatter';
import type { KpiValue } from './base';
import type { MtdKpiItem } from './mtd'

export interface ValueConfig {
    value?: number;
    divider: number;
    unit: string;
    formatter?: (value: number, withSpace?: boolean, withSeparator?: boolean, precision?: number) => string;
    precision?: number;
}

function calculateKpiValue(kpi_name: string, { value, divider, unit, formatter, precision }: ValueConfig): KpiValue {
    const calculatedValue = RoundFloat(value ? value / divider : 0, precision || 2);
    let valueText = formatMetric(value || 0, true, true, precision || 2);
    if (formatter) {
        valueText = formatter(value || 0, true, true, precision || 2);
    }
    return {
        kpi_name,
        value: calculatedValue,
        value_text: valueText,
        unit: unit
    };
}

function createGrowth(current: number, last: number): { growth: number, growth_text: string } {
    if (last !== 0) {
        const value = RoundFloat((current - last) / last * 100, 1);
        const sign = value > 0 ? '+' : '';
        return {
            growth: value,
            growth_text: `${sign}${value}%`
        }
    }

    return { growth: 0, growth_text: 'no data' }
}

export const KpiUtil = {
    calculateKpiValue,
    createGrowth
}