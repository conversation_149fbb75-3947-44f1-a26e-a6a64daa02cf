import type { Circle } from "$lib/report";
import { formatDateToMonthYear } from "$lib/utils/formatter";
import type { KpiValue } from "./base";

export interface FmKpiItem extends KpiValue {
    month_date?: number;
    month_name?: string;
}

export interface FmKpiValue {
    kpi_name: string;
    unit: string;
    entity_name?: string;
    value?: number;
    value_text?: string;
    month_date: number;
    month_name?: string;
    growth?: number;
    growth_text?: string;
}

export type FmCircleKpiValue = {
    kpi_name: string;
    unit: string;
    month_date: number;
} & {
    [c in Circle]?: number;
}

function createFmKpiItem(kpi_value: KpiValue, month_date: number): FmKpiItem {
    const month_name = formatDateToMonthYear(month_date);
    return {
        month_date,
        month_name,
        ...kpi_value
    };
}

export const FmUtil = {
    createFmKpiItem
}