export function compareArray<T extends string | number | boolean>(arr1: T[], arr2: T[], same_order: boolean = true): boolean {
  if (arr1.length !== arr2.length) return false;
  if (!same_order) {
    arr1 = [...arr1].sort();
    arr2 = [...arr2].sort();
  }

  return arr1.every((val, index) => val === arr2[index]);
}

export function getDifferentFields<T extends Record<string, any>>(obj1: T, obj2: T): string[] {
  return Object.keys(obj1).filter(key => obj1[key] !== obj2[key]);
}


export class Register<T> {
  private _register: T[] = [];

  /**
   * Adds an element to the register.
   * @param item The element to add to the register.
   */
  push(item: T): void {
    this._register.push(item);
  }

  /**
   * Removes and returns the last element from the register.
   * @returns The last element from the register, or undefined if the register is empty.
   */
  pop(): T | undefined {
    return this._register.pop();
  }

  /**
   * Retrieves the top element of the register without removing it.
   * @returns The top element of the register, or undefined if the register is empty.
   */
  peek(): T | undefined {
    return this._register[this._register.length - 1];
  }

  /**
   * Checks if the register is empty.
   * @returns {boolean} True if the register is empty, false otherwise.
   */
  isEmpty(): boolean {
    return this._register.length === 0;
  }

  exists(item: T): boolean {
    return this._register.includes(item);
  }

  remove(item: T): void {
    const index = this._register.indexOf(item);
    if (index !== -1) {
      this._register.splice(index, 1);
    }
  }

  moveToTop(item: T): void {
    this.remove(item);
    this.push(item);
  }

  /**
   * Clears the resgister.
   */
  clear(): void {
    this._register = [];
  }

  /**
   * length of the register
  */
  get length(): number {
    return this._register.length;
  }

  /**
   * Prints the register to the console.
   */
  printStack(): void {
    console.log(this._register);
  }
}