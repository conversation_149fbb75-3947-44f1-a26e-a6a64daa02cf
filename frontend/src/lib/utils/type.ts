export type PartialBy<T, K extends keyof T> = Omit<T,K> & Partial<Pick<T,K>>;
export type TransformProps<T, K> = Omit<T, keyof K> & K;

export type CommonProps<T, U> = {
  [K in Extract<keyof T, keyof U>]: T[K];
};

export type NumericKeys<T> = {
  [K in keyof T]: T[K] extends number | undefined ? K : never;
}[keyof T]

export function isExcelFileType(ftype: string): boolean {
    console.log("file type:", ftype);
    return excelMime.includes(ftype.toLowerCase());
}

const excelMime: string[] = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel.sheet.binary.macroenabled.12',
    'application/wps-office.xlsx',
]