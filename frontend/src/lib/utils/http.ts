
export interface BaseResponse<T> {
  request_id: string;
  code: number;
  message: string;
  data: T;
  error?: string;
  pagination?: {
    page: number;
    total: number;
  }
}

export type ProgressCallFn = (ev: ProgressEvent<XMLHttpRequestEventTarget>) => void;
export interface HttpResponse<T> extends Response {
  payload?: T;
}

export interface HttpCallStatus {
  status_text: string;
  status_code: number;
}

interface requestOptions {
  headers: Map<string, string>;
  form?: FormData;
  timeout?: number;
  signal?: AbortSignal;
  payload?: BodyInit;
  params?: URLSearchParams;
}

function newRequestOptions(): requestOptions {
  return {
    headers: new Map<string, string>()
  }
}

export type RequestOption = (opt: requestOptions) => void;

export type RequestItem = Headers | RequestInfo;

export interface RequestInjector {
  inject(request: Request): Request;
}

export function NewHeaderInjector(headerItem: Map<string, string>): RequestInjector {
  return {
    inject: (request: Request): Request => {
      // const headers = request.headers;
      headerItem.forEach((val, key) => {
        request.headers.set(key, val);
      });
      
      return request;
    },
  };
}

export class HttpClient {
  private injectors: RequestInjector[] = [];
  // private host: string;
  
  constructor(...injectors: RequestInjector[]) {
    if (injectors) this.injectors = injectors;
  }
  
  async Get<T>(url: URL, ...options: RequestOption[]): Promise<HttpResponse<T>> {
    const opt = newRequestOptions();
    options.forEach(val => {
      val(opt);
    });
    
    const req = new Request(url.toString(), { method: 'get' });
    
    if (opt.headers.size > 0) {
      opt.headers.forEach((val: string, key: string) => {
        req.headers.set(key, val);
      })
    }
    
    return this._call<T>(req, opt);
  }
  
  async Post<T>(url: URL, body: BodyInit, ...options: RequestOption[]): Promise<HttpResponse<T>> {
    const opt = newRequestOptions();
    options.forEach(val => {
      val(opt);
    });
    
    const headers = createHeaders(opt.headers);
    if(!headers.has('Content-Type')) headers.set('Content-Type', 'application/json');
    const req = new Request(url.toString(), { method: 'post', body: body, headers: headers });
    return this._call<T>(req, opt);
  }
  
  async UploadFile<T>(url: URL, f: File | File[], ...options: RequestOption[]): Promise<HttpResponse<T>> {
    const opt = newRequestOptions();
    options.forEach(val => {
      val(opt);
    });
    
    const headers = createHeaders(opt.headers);
    let formData = new FormData();
    if (opt.form) formData = opt.form;
    if(Array.isArray(f)) {
      f.forEach(val => {
        formData.append('file', val, val.name);
      })
    } else {
      formData.append('file', f)
    }
    
    const req = new Request(url.toString(), { method: 'post', body: formData, headers: headers });
    return this._call<T>(req, opt);
  }
  
  async UploadFileWithProgress(url: URL, f: File | File[], progressCall: (ev: ProgressEvent<XMLHttpRequestEventTarget>) => void, resFn: (resBody: string) => void, ...options: RequestOption[]): Promise<void> {
    const opt = newRequestOptions();
    options.forEach(val => {
      val(opt);
    });
    
    let headers = createHeaders(opt.headers);
    let fetchReq = new Request(url, {headers: headers});
    if (this.injectors.length > 0) {
      this.injectors.forEach((val) => {
        fetchReq = val.inject(fetchReq);
      });
    }
    
    headers = fetchReq.headers;
    
    let data = new FormData();
    if (opt.form) data = opt.form;
    if(Array.isArray(f)) {
      f.forEach(val => {
        data.append('file', val, val.name);
      })
    } else {
      data.append('file', f)
    }
    
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      try {
        req.open('POST', url.toString(), true);
        
        headers.forEach((val, key) => {
          req.setRequestHeader(key, val);
        })
        
        req.upload.addEventListener('progress', progressCall);
        
        req.upload.addEventListener('loadend', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
          console.log('LOADEND:', {total: ev.total, loaded: ev.loaded, eventPhase: ev.eventPhase});
        })
        
        req.upload.addEventListener('loadstart', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
          console.log('LOADSTART:', ev);
          
        })
        
        // request finished event
        req.addEventListener('load', function(e) {
          // HTTP status message (200, 404 etc)
          console.log(req.status);
          
          // req.response holds response from the server
          console.log(req.response);
          resFn(req.responseText);
          resolve();
        });
        
        req.onreadystatechange = function() {
          if (req.readyState == XMLHttpRequest.DONE) {
            console.log('onreadystatechange', req.readyState);
            resolve();
          }
        }
        
        // send POST request to server
        req.send(data);
      } catch (err: unknown) {
        reject((err as Error).message);
      }
    })
  }
  
  async UploadFormWithProgress<T>(url: URL, data: FormData, progressCall: ProgressCallFn, resFn: (res: string) => void, ...options: RequestOption[]): Promise<void> {
    const opt = newRequestOptions();
    options.forEach(val => {
      val(opt);
    });
    
    let headers = createHeaders(opt.headers);
    let fetchReq = new Request(url, {headers: headers});
    if (this.injectors.length > 0) {
      this.injectors.forEach((val) => {
        fetchReq = val.inject(fetchReq);
      });
    }
    
    headers = fetchReq.headers;
    return new Promise((resolve, reject) => {
      const req = new XMLHttpRequest();
      try {
        req.open('POST', url.toString(), true);
        
        headers.forEach((val, key) => {
          req.setRequestHeader(key, val);
        })
        
        req.upload.addEventListener('progress', progressCall);
        
        req.upload.addEventListener('loadend', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
          console.log('LOADEND:', {total: ev.total, loaded: ev.loaded, eventPhase: ev.eventPhase});
        })
        
        // request finished event
        req.addEventListener('load', function(e) {
          // HTTP status message (200, 404 etc)
          console.log(req.status);
          
          // req.response holds response from the server
          console.log(req.response);
          resFn(req.responseText);
          resolve();
        });
        
        req.onreadystatechange = function(ev) {
          if (req.readyState == XMLHttpRequest.DONE) {
            console.log('onreadystatechange', req.readyState);
            resolve();
          }
        }
        
        // send POST request to server
        req.send(data);
      } catch (err: unknown) {
        reject((err as Error).message);
      }
    })
  }
  
  async PostMultipartForm<T>(
    url: URL, 
    form: FormData,
    headers?: Headers,
    onProgress?: (ev: ProgressEvent) => void, 
    onSuccess?: (body: T | undefined, xhr: XMLHttpRequest) => void,
    onError?: (ev: ProgressEvent) => void
    ): Promise<void> {
      let fetchReq = new Request(url, {headers: headers});
      if (this.injectors.length > 0) {
        this.injectors.forEach((val) => {
          fetchReq = val.inject(fetchReq);
        });
      }
      
      const fetchHeaders = fetchReq.headers;
      return new Promise((resolve, reject) => {
        const req = new XMLHttpRequest();
        try {
          req.open('POST', url.toString());
          fetchHeaders.forEach((val, key) => {
            req.setRequestHeader(key, val);
          })
          
          let prevLoaded = 0;
          req.upload.addEventListener('progress', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
            if (prevLoaded !== 0 && ev.loaded <= prevLoaded) {
              // reject(new Error('aborted'));
              console.debug('aborted:', req);
              // req.abort();
              return;
            }
            prevLoaded = ev.loaded;
            if (onProgress) onProgress(ev);
          })
          
          req.upload.addEventListener('loadend', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
            console.debug('LOADEND:', {total: ev.total, loaded: ev.loaded, eventPhase: ev.eventPhase});
          })
          
          // request finished event
          // req.addEventListener('load', (ev: ProgressEvent<XMLHttpRequestEventTarget>) => {
          //     // HTTP status message (200, 404 etc)
          //     console.log('load status:', req.status);
          
          //     // req.response holds response from the server
          //     console.log(req.response);
          //     const res: T = JSON.parse(req.response);
          //     if (onSuccess) onSuccess(res, req);
          //     resolve();
          // });
          
          req.onload = function (ev: ProgressEvent<EventTarget>) {
            let res: BaseResponse<T> | undefined = undefined;
            if (this.responseText !== '') {
              res = JSON.parse(this.responseText);
            }
            
            if (this.status >= 200 && this.status < 300) {
              if (onSuccess) onSuccess(res?.data, this);
              resolve()
            } else {
              let err = this.statusText;
              if (res) err = res.error??err;
              if (onError) onError(ev);
              reject(new Error(err));
            }
          }
          
          // req.onreadystatechange = function(ev) {
          //     if (req.readyState == XMLHttpRequest.DONE) {
          //         if (req.responseText !== '') {
          //             const res = JSON.parse(req.response);
          //             if (req.status !== 200) {
          //                 if (res.error) {
          //                     console.error('error:', res.error)
          //                     reject(new Error(res.error));
          //                 }
          //             }
          
          //         }
          
          //         resolve();
          //     }
          // }
          
          req.onerror = function(ev: ProgressEvent<EventTarget>) {
            if (onError) onError(ev);
          }
          
          req.onabort = function(ev: ProgressEvent<EventTarget>) {
            reject(new Error('aborted'));
            if (onError) onError(ev);
            console.debug('abort:', req.response);
          }
          
          // send POST request to server
          req.send(form);
        } catch (err: unknown) {
          reject((err as Error).message);
        }
      })
    }
    
    async Put<T>(url: URL, body: BodyInit, ...options: RequestOption[]): Promise<HttpResponse<T>> {
      const opt = newRequestOptions();
      options.forEach(val => {
        val(opt);
      });
      
      const headers = createHeaders(opt.headers);
      if(!headers.has('Content-Type')) headers.set('Content-Type', 'application/json');
      const req = new Request(url.toString(), { method: 'put', body: body, headers: headers });
      return this._call<T>(req, opt);
    }
    
    async Delete<T>(url: URL, ...options: RequestOption[]): Promise<HttpResponse<T>> {
      const opt = newRequestOptions();
      options.forEach(val => {
        val(opt);
      });
      
      const headers = createHeaders(opt.headers);
      if(!headers.has('Content-Type')) headers.set('Content-Type', 'application/json');
      const req = new Request(url.toString(), { method: 'delete', body: JSON.stringify(opt.payload), headers: headers });
      return this._call<T>(req, opt);
    }
    
    async Raw(method: string, url: URL, body?: BodyInit, ...options: RequestOption[]): Promise<Response> {
      let headerInit: HeadersInit | undefined = undefined;
      const opt = newRequestOptions()
      options.forEach(fn => {
        fn(opt);
      })
      
      if (opt.headers.size > 0) {
        headerInit = new Headers()
        for(const [key, value] of opt.headers) {
          headerInit.set(key, value)
        }
      }
      
      let req = new Request(url.toString(), { method: method, body: body, headers: headerInit });
      
      if (this.injectors.length > 0) {
        this.injectors.forEach((val) => {
          req = val.inject(req);
        });
      }
      
      return fetch(req, {credentials: 'omit', signal: opt.signal});
    }
    
    async _call<T>(request: Request, options: requestOptions): Promise<HttpResponse<T>> {
      if (this.injectors.length > 0) {
        this.injectors.forEach((val) => {
          request = val.inject(request);
        });
      }
      // const response: HttpResponse<T> = await fetch(request, { credentials: 'omit' });
      
      let response: HttpResponse<T>;
      try {
        response = await fetch(request, { credentials: 'omit', signal: options.signal });
      } catch (err) {
        return Promise.reject(new Error("Could not connect to the server - " + err));
      }
      
      if (response.ok) {
        response.payload = await response.json();
      } else {
        try {
          response.payload = await response.json();
        } catch (error) {
          return Promise.reject(new Error(response.statusText))
        }
      }
      return Promise.resolve(response);
    }
    
    
  }
  
  function createHeaders(initHeaders: Map<string,string>): Headers {
    const h = new Headers();
    for(const [key, value] of initHeaders) {
      h.set(key, value)
    }
    
    return h;
  }
  
  export function WithHeader(key: string, value: string): RequestOption {
    return function(opt: requestOptions): void {
      opt.headers.set(key, value);
    }
  }
  
  export function WithFormData(key: string, value: string): RequestOption {
    return function(opt: requestOptions): void {
      if (!opt.form) {
        opt.form = new FormData();
      }
      
      opt.form.append(key, value);
    }
  }
  
  export function WithFile(key: string, f: File): RequestOption {
    return function(opt: requestOptions): void {
      if (!opt.form) {
        opt.form = new FormData;
      }
      
      opt.form.append(key, f, f.name);
    }
  }
  
  export function WithTimeout(duration: number): RequestOption {
    return function(opt: requestOptions): void {
      opt.timeout = duration;
    }
  }
  
  export function WithAbortSignal(signal: AbortSignal): RequestOption {
    return function(opt: requestOptions): void {
      opt.signal = signal;
    }
  }
  
  export function WithParams(params: URLSearchParams): RequestOption {
    return function(opt: requestOptions): void {
      opt.params = params;
    }
  }
