import dayjs from "dayjs";

export const currency = new Intl.NumberFormat('id', {
    style: 'currency',
    currency: 'IDR', 
    currencyDisplay: "code",
    // These options are needed to round to whole numbers if that's what you want.
    minimumFractionDigits: 0, // (this suffices for whole numbers, but will print 2500.10 as $2,500.1)
    //maximumFractionDigits: 0, // (causes 2500.99 to be printed as $2,501)
  });

export function formatNumberDecimal(value: number | null | undefined, locale = 'en-US', signed: boolean = false): string {
  if (value == null) return '0';
  if (signed && value > 0) return `+${new Intl.NumberFormat(locale).format(value)}`;
  return new Intl.NumberFormat(locale).format(value);
}

export function RoundFloat(num: number, precision: number): number {
  const ratio = Math.pow(10, precision);
  return Math.round(num * ratio) / ratio;
}

export function formatBytes(bytes: number, withSpace: boolean = true, withSeparator: boolean = true, precision: number = 2): string {
  let space = '';
  if (withSpace) space = ' ';
  let num: string;
  let unit: string;

  if (bytes < 1024) {
    num = bytes.toString();
    unit = 'Bytes'
  } else if (bytes < 1024 * 1024) {
    const kb = RoundFloat(bytes / 1024, precision);
    num = kb % 1 === 0 ? kb.toFixed(0) : kb.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(kb);
    unit = 'KB';
  } else if (bytes < 1024 * 1024 * 1024) {
    const mb = RoundFloat(bytes / (1024 * 1024), precision);
    num = mb % 1 === 0 ? mb.toFixed(0) : mb.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(mb);
    unit = 'MB';
  } else if (bytes < 1024 * 1024 * 1024 * 1024) {
    const gb = RoundFloat(bytes / (1024 * 1024 * 1024), precision);
    num = gb % 1 === 0 ? gb.toFixed(0) : gb.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(gb);
    unit = 'GB'
  } else if (bytes < 1024 * 1024 * 1024 * 1024 * 1024) {
    const tb = RoundFloat(bytes / (1024 * 1024 * 1024 * 1024), precision);
    num = tb % 1 === 0 ? tb.toFixed(0) : tb.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(tb);
    unit = 'TB';
  } else {
    const pb = RoundFloat(bytes / (1024 * 1024 * 1024 * 1024 * 1024), precision);
    num = pb % 1 === 0 ? pb.toFixed(0) : pb.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(pb);
    unit = 'PB';
  }

  return `${num}${space}${unit}`;
}

export function formatMetric(value: number, withSpace: boolean = true, withSeparator: boolean = true, precision: number = 2): string {
  let space = '';
  if (withSpace) space = ' ';
  let num: string;
  let unit: string;

  if (value < 1000) {
    num = value.toString();
    unit = '';
  } else if (value < 1000000) {
    const k = RoundFloat(value / 1000, precision);
    num = k % 1 === 0 ? k.toFixed(0) : k.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(k);
    unit = 'k';
  } else if (value < 1000000000) {
    const m = RoundFloat(value / 1000000, precision);
    num = m % 1 === 0 ? m.toFixed(0) : m.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(m);
    unit = 'Mn';
  } else {
    const b = RoundFloat(value / 1000000000, precision);
    num = b % 1 === 0 ? b.toFixed(0) : b.toFixed(precision);
    if (withSeparator) num = formatNumberDecimal(b);
    unit = 'Bn';
  }

  return `${num}${space}${unit}`;
}

const dayjsDateFormats = [
  'YYYY/MM/DD',
  'MM/DD/YYYY',
  'DD MMM YYYY',
  'DD-MM-YYYY',
  'YYYY-MM-DD'
] as const;

export type DateFormat = typeof dayjsDateFormats[number];

const dayjsDateTimeFormats = [
  'YYYY/MM/DD HH:mm:ss',
  'MM/DD/YYYY HH:mm:ss',
  'DD MMM YYYY HH:mm:ss',
  'DD-MM-YYYY HH:mm:ss',
  'YYYY-MM-DD HH:mm:ss'
] as const;

export type DateTimeFormat = typeof dayjsDateTimeFormats[number];

export function formatDate(date: number | Date, format: DateFormat): string {
  return formatDayjs(date, format);
}

export function formatDateTime(date: number | Date, format: DateTimeFormat): string {
  return formatDayjs(date, format);
}

export function formatToMonthID(date: number | Date): string {
  return formatDayjs(date, 'YYYYMM');
}

function formatDayjs(date: number | Date, format: string): string {
  if (date === 0) return '';
  const d = dayjs(date);
  return d.format(format);
}

export function formatDateToMonthYear(date: number | Date): string {
  return formatDayjs(date, 'MMM YYYY');
}