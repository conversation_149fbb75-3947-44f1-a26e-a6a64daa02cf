export interface Percentiles {
    min: number;
    q1: number;
    median: number;
    q3: number;
    max: number;
}

export function calculatePercentiles(values: number[]): Percentiles {
    const sorted = [...values].sort((a, b) => a - b);
    const len = sorted.length;

    return {
        min: sorted[0],
        q1: sorted[Math.floor(len * 0.25)],
        median: sorted[Math.floor(len * 0.5)],
        q3: sorted[Math.floor(len * 0.75)],
        max: sorted[len - 1],
    };
}

function getPercentileRank(value: number, stats: Percentiles): number {
    const { min, max }= stats;
    if (min === max) return 50;
    return ((value - min) / (max - min)) * 100;
}

export type HeatmapColors = readonly [
    string, string, string, string, string, string, string, string, string, string
]

const DEFAULT_HEATMAP_COLORS: HeatmapColors = [
    '#e76476',
    '#ed8778',
    '#f2a986',
    '#f5c69d',
    '#f8e1bd',
    '#e8e4bd',
    '#d0d4a3',
    '#b1c384',
    '#8fb568',
    '#6da54c'
];

export function getHeatmapColorFromPercentiles(value: number, stats: Percentiles, colors: HeatmapColors = DEFAULT_HEATMAP_COLORS): string {
    const percentileRank = getPercentileRank(value, stats);
    const colorIndex = Math.min(Math.floor(percentileRank / 10), 9);
    return colors[colorIndex];
}
