export function hexToRGBA(hex: string, opacity: number = 1): string {
  // Remove # if present
  hex = hex.replace('#', '');
  
  // Parse hex values
  let r, g, b;
  
  if (hex.length === 3) {
    // Convert short hex (#RGB) to full hex (#RRGGBB)
    r = parseInt(hex.charAt(0) + hex.charAt(0), 16);
    g = parseInt(hex.charAt(1) + hex.charAt(1), 16);
    b = parseInt(hex.charAt(2) + hex.charAt(2), 16);
  } else {
    r = parseInt(hex.substring(0, 2), 16);
    g = parseInt(hex.substring(2, 4), 16);
    b = parseInt(hex.substring(4, 6), 16);
  }
  
  // Ensure opacity is between 0 and 1
  opacity = Math.min(1, Math.max(0, opacity));
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

export function hslToRGBA(h: number, s: number, l: number, opacity: number = 1): string {
  // Convert S and L to decimal
  s /= 100;
  l /= 100;

  // Algorithm from https://www.w3.org/TR/css-color-3/#hsl-color
  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;

  let r = 0, g = 0, b = 0;

  if (h >= 0 && h < 60) {
    r = c; g = x; b = 0;
  } else if (h >= 60 && h < 120) {
    r = x; g = c; b = 0;
  } else if (h >= 120 && h < 180) {
    r = 0; g = c; b = x;
  } else if (h >= 180 && h < 240) {
    r = 0; g = x; b = c;
  } else if (h >= 240 && h < 300) {
    r = x; g = 0; b = c;
  } else if (h >= 300 && h < 360) {
    r = c; g = 0; b = x;
  }

  // Convert to 0-255 range and add m
  r = Math.round((r + m) * 255);
  g = Math.round((g + m) * 255);
  b = Math.round((b + m) * 255);

  // Ensure opacity is between 0 and 1
  opacity = Math.min(1, Math.max(0, opacity));

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
}

export function convertToRGBA(color: string, opacity: number = 1): string {
  if (color.startsWith('#')) {
    return hexToRGBA(color, opacity);
  } else if (color.startsWith('hsl')) {
    let hslValue: [number, number, number] = [0, 0, 0];
    if (color.includes('var') && color.includes('--')) {
      const cssVarName = extractCssVarName(color);
      const nums = extractCssVarValue(cssVarName).replaceAll('%', '').split(' ').map(num => parseFloat(num));
      if (nums && nums.length === 3) hslValue = [nums[0], nums[1], nums[2]];
    } else {
      hslValue = color.match(/\d+/g)?.map(Number) as [number, number, number];
    }
    const [h, s, l] = hslValue;
    return hslToRGBA(h, s, l, opacity);
  } else {
    return color;
  }
}

function extractCssVarName(str: string): string {
  str = str.substring(str.indexOf('var') + 3);
  str = str.substring(str.indexOf('(') + 1, str.indexOf(')'));
  return str;
}

function extractCssVarValue(name: string): string {
  const value = getComputedStyle(document.body).getPropertyValue(name);
  return value;
}