export const base64url = {
    encode: function(str: string): string {
        return btoa(str)
            .replace(/=/g, '')
            .replace(/\+/g, '-')
            .replace(/\//g, '_');
    },

    decode: function(encodedString: string): string {
        const input = encodedString
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        return atob(padstring(input));
    }

}

function padstring(input: string): string {
    const pad = input.length % 4;
    if(pad) {
        if(pad === 1) {
            throw new Error('InvalidLengthError: Input base64url string is the wrong length to determine padding');
        }
        input += new Array(5-pad).join('=');
    }

    return input;
}