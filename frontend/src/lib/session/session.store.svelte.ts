import { browser } from '$app/environment';

const localTokenKey = 'token';
const localOAuthTokenKey = 'oauthToken';

interface Auth {
    username: string;
    email: string;
    fullname: string;
    token: string;
    is_admin: boolean;
    status_flag: 'ACTIVE' | 'NEED_RESET_PASSWORD';
    expiry: number;
    groups: string[];
}

type AuthState = 'LoggedOut' | 'LoggedIn' | 'TokenInvalid' | 'TokenExpired' | 'TokenRefreshed' | 'ResetPassword';

function generateClientID(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

let _token: string | null = null;
let _oauthToken: string | null = null;
let _clientID = getClientID();

function getClientID(): string {
    let clientID = null as string | null;
    if (browser) clientID = localStorage.getItem('clientID');
    if (clientID) {
        return clientID;
    }
    const newClientID = generateClientID();
    if (browser) localStorage.setItem('clientID', newClientID);
    return newClientID;
}

// const SessionStore = $state({
//     auth: null as Auth | null,
//     authState: null as AuthState | null,
//     instance: Date.now().toString(),
//     clientID: getClientID(),
//     get token(): string | null {
//         if (_token === null) {
//             _token = localStorage.getItem(localTokenKey);
//         }
//         return _token;
//     },

//     setAuth(auth: Auth | null) {
//         if (auth === null) {
//             this.auth = null;
//             this.authState = 'LoggedOut';
//             _token = null;
//             return;
//         }
//         this.auth = auth;
//         this.authState = auth.status_flag === 'ACTIVE' ? 'LoggedIn' : 'ResetPassword';
//         _token = auth.token;
//     },
// });

// export function useSessionStore() {
//     return SessionStore;
// }

let _auth = $state<Auth | null>(null);
let _authState = $state<AuthState | null>(null);

let _oauth = $state<Record<string, any> | null>(null);

function getLocalToken(): string | null {
    if (browser) return localStorage.getItem(localTokenKey);
    return null;
}

function setLocalToken(token: string) {
    if (browser) localStorage.setItem(localTokenKey, token);
}

function removeLocalToken() {
    if (browser) localStorage.removeItem(localTokenKey);
}

function getLocalOAuthToken(): string | null {
    if (browser) return localStorage.getItem(localOAuthTokenKey);
    return null;
}

function setLocalOAuthToken(token: string) {
    if (browser) localStorage.setItem(localOAuthTokenKey, token);
}

function removeLocalOAuthToken() {
    if (browser) localStorage.removeItem(localOAuthTokenKey);
}

export function useSessionStore() {
    return {
        get auth() { return _auth},
        get authState() { return _authState},
        instance: Date.now().toString(),
        get token(): string | null {
            if (_token === null) {
                _token = getLocalToken();
            }
            return _token;
        },

        get clientID(): string {
            return _clientID;
        },

        setAuth(auth: Auth | null) {
            if (auth === null) {
                _auth = null;
                _authState = 'LoggedOut';
                _token = null;
                removeLocalToken();
                return;
            }
            _auth = auth;
            _authState = auth.status_flag === 'ACTIVE' ? 'LoggedIn' : 'ResetPassword';
            _token = auth.token;
            setLocalToken(auth.token);
        },

        setOAuth(oauth: Record<string, any> | null) {
            if (oauth === null) {
                _oauth = null;
                _authState = 'LoggedOut';
                _oauthToken = null;
                removeLocalOAuthToken();
                return;
            }
            _oauth = oauth;
            _authState = oauth.status_flag === 'ACTIVE' ? 'LoggedIn' : 'ResetPassword';
            _oauthToken = oauth.token;
            setLocalOAuthToken(oauth.token);
        },

        setAuthState(authState: AuthState) {
            _authState = authState;
        }
    };
};

export type { Auth, AuthState };