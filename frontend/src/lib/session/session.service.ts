import { type Auth, useSessionStore } from './session.store.svelte';
import { useApiService, type APIRoute } from '../api';
import { WithParams } from '$lib/utils/http';

const IDLE_TIME_THRESHOLD: number = 600000;
const TICKER: number = 5000;

export class SessionService {
    idleTimer?: number;
    idleTime: number = 0;

    constructor() {

    }

    async Authenticate(user: string, password: string): Promise<Auth> {
        const api = useApiService();
        const store = useSessionStore();
        const payload = {
            username: user,
            password: password,
        }

        const auth = await api.Post<Auth>('/auth/authenticate', false, payload)
        store.setAuth(auth);
        api.setToken(auth.token);
        this.StartIdleTimer();
        return auth;
    }

    async AuthenticateWithGoogleOAuth(params: URLSearchParams): Promise<Auth> {
        const api = useApiService();
        const store = useSessionStore();
        const auth = await api.Get<Auth>('/google-oauth/callback', false, WithParams(params));
        store.setAuth(auth);
        api.setToken(auth.token);
        this.StartIdleTimer();
        return auth;
    }

    async Validate(token: string): Promise<Auth> {
        const api = useApiService();
        const store = useSessionStore();
        try {
            const auth = await api.Get<Auth>('/auth/validate', true)
            store.setAuth(auth);
            api.setToken(token);
            this.StartIdleTimer();
            return auth;
        } catch (error) {
            const err = error as Error;
            // if (err.name === APIError.UNAUTHORIZED) {
            this.Logout();
            // }
            return Promise.reject(err);
        }
    }

    private async RefreshToken(): Promise<boolean> {
        const api = useApiService();
        const store = useSessionStore();
        return api.Get<Auth>('/auth/refresh', true)
            .then(res => {
                const token = res.token;
                store.setAuth(res);
                api.setToken(token);
                return true;
            });

    }

    Logout() {
        const api = useApiService();
        const store = useSessionStore();
        store.setAuth(null);
        store.setAuthState('LoggedOut');
        api.removeToken();
        this.ClearIdleTimer();
    }

    // Check if the session is valid
    async CheckSession(): Promise<boolean> {
        const api = useApiService();
        const store = useSessionStore();
        const token = store.token;
        if (!token) {
            store.setAuthState('LoggedOut');
            return false;
        }

        api.setToken(token);
        if (store.authState === 'ResetPassword') return false;
        if (store.authState == 'LoggedIn') return true;

        try {
            await this.Validate(token);
            return true;
        } catch (error) {
            return false
        }
    }

    StartIdleTimer() {
        document.onclick = () => {
            this.idleTime = 0;
        }

        document.onmousemove = () => {
            this.idleTime = 0;
        }

        document.ontouchmove = () => {
            this.idleTime = 0;
        }

        document.onscroll = () => {
            this.idleTime = 0;
        }

        this.idleTimer = window.setInterval(this.checkIdleTime.bind(this), TICKER);
    }

    public ClearIdleTimer(): void {
        clearTimeout(this.idleTimer);
    }

    public GetTokenExpiryDuration(): number {
        const sess = useSessionStore();
        const expiry = sess.auth?.expiry
        if (!expiry) return 0;
        return new Date(expiry).valueOf() - new Date().valueOf();
    }

    private checkIdleTime() {
        const sess = useSessionStore();
        this.idleTime += TICKER;
        const expiry = this.GetTokenExpiryDuration();

        if (expiry < 60000) {
            if (this.idleTime < IDLE_TIME_THRESHOLD) {
                this.RefreshToken();
            } else {
                sess.setAuthState('TokenExpired');
            }
        }
    }
}

let sessionService: SessionService;

export function useSessionService(): SessionService {
    if (!sessionService) {
        sessionService = new SessionService();
    }

    return sessionService;
}
