import { useSessionStore } from './session.store.svelte';

function initGoogleAuth() {
    const clientId = import.meta.env.VITE_GOOGLE_CLIENT_ID;
    const clientSecret = import.meta.env.VITE_GOOGLE_CLIENT_SECRET;
    const redirectUri = import.meta.env.VITE_GOOGLE_REDIRECT_URI;

    return new GoogleOAuthService(clientId, redirectUri);
}

export class GoogleOAuthService {
    private authUrl: string;
    private sess = useSessionStore();

    constructor(private clientId: string, private redirectUri: string) {
        const authUrl = `https://accounts.google.com/o/oauth2/v2/auth?
            client_id=${this.clientId}&
            redirect_uri=${this.redirectUri}&
            response_type=token&
            scope=openid%20profile%20email&
            prompt=select_account`;

        this.authUrl = authUrl.replace(/\s/g, '');
        console.log('authUrl', this.authUrl);
    }

    handleLogin() {
        window.location.href = this.authUrl;
    }

    async fetchUserInfo(token: string): Promise<any> {
        try {
            const response = await fetch('https://www.googleapis.com/oauth2/v3/userinfo', {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            });

            const data = await response.json();
            this.sess.setOAuth(data);
            return data;
        } catch (error) {
            console.error('Error fetching user info:', error);
            return null;
        }

    }

    async logout() {
        this.sess.setOAuth(null);
    }

}

let googleOAuthService: GoogleOAuthService;

export function useGoogleOAuthService() {
    if (!googleOAuthService) {
        googleOAuthService = initGoogleAuth();
    }
    return googleOAuthService;
}