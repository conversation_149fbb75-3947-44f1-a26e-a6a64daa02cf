// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		// interface Platform {}
	}
}

import '@tanstack/table-core';
import type { CellContext } from '@tanstack/table-core';

declare module '@tanstack/table-core' {
	interface ColumnMeta<TData extends RowData, TValue> {
		getColor?: (props: CellContext<TData, TValue>) => string;
	}
}

export {};
