package http

import (
	"net/http"

	htx "github.com/likearthian/apikit/transport/http"

	"github.com/csee-pm/dashboard-pm/internal/report"
)

type ReportHandler struct {
	GetReportHandler http.Handler
}

func CreateReportHandler(reportApi *report.ReportAPI, options ...htx.ServerOption) ReportHandler {
	return ReportHandler{
		GetReportHandler: htx.NewServer(
			reportApi.GetReport,
			htx.CommonGetRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
	}
}
