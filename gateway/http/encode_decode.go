package http

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/errs"
	md "github.com/go-chi/chi/v5/middleware"
	"github.com/likearthian/apikit/logger"
	"github.com/likearthian/apikit/transport"
	htx "github.com/likearthian/apikit/transport/http"
)

func errorEncoder(ctx context.Context, err error, w http.ResponseWriter) {
	code := errs.Err2Code(err)
	w.<PERSON>er().Set(htx.HeaderContentType, htx.HttpContentTypeJson)
	w.WriteHeader(code)
	reqid, ok := ReqIDFromContext(ctx)
	if !ok {
		reqid = ""
	}

	response := errorWrapper{Error: err.Error(), ReqID: reqid, Code: code}
	json.NewEncoder(w).Encode(response)
}

type errorWrapper struct {
	ReqID string `json:"request_id"`
	Code  int    `json:"code"`
	Error string `json:"error"`
}

func createHttpLogErrorHandler(logger logger.Logger) transport.ErrorHandlerFunc {
	return func(ctx context.Context, err error) {
		code := errs.Err2Code(err)
		if code == 500 {
			logger.Error(err.Error(), "event", "http request error")
		}
	}
}

func ReqIDFromContext(ctx context.Context) (string, bool) {
	reqID, ok := ctx.Value(md.RequestIDKey).(string)
	if !ok {
		return "", false
	}
	return reqID, ok
}

func CommonJSONResponseEncoder[T any](ctx context.Context, w http.ResponseWriter, response T) error {
	w.Header().Set(htx.HeaderContentType, htx.HttpContentTypeJson)
	reqID, _ := ReqIDFromContext(ctx)

	payload := dto.SuccessResponse(reqID, response)
	var gw io.Writer = w
	if needGzipped(ctx) {
		w.Header().Set("Content-Encoding", "gzip")
		gz := gzip.NewWriter(w)
		defer gz.Close()
		gw = gz
	}

	return json.NewEncoder(gw).Encode(payload)
}

func CommonPagedJSONResponseEncoder[T any](ctx context.Context, w http.ResponseWriter, response dto.PagedData[T]) error {
	w.Header().Set(htx.HeaderContentType, htx.HttpContentTypeJson)
	reqID, _ := ReqIDFromContext(ctx)

	payload := dto.SuccessResponse(reqID, response.Data, response.Pagination)
	var gw io.Writer = w
	if needGzipped(ctx) {
		w.Header().Set("Content-Encoding", "gzip")
		gz := gzip.NewWriter(w)
		defer gz.Close()
		gw = gz
	}

	return json.NewEncoder(gw).Encode(payload)
}

type requestDecoderOption struct {
	acceptedFields  map[string]struct{}
	urlParamsGetter func(context.Context) map[string]string
}

func getAcceptFromContext(ctx context.Context) string {
	val := ctx.Value(htx.ContextKeyRequestAccept)
	enc, ok := val.(string)
	if ok {
		encodings := strings.Split(strings.ToLower(enc), ",")
		return strings.TrimSpace(encodings[0])
	}

	return ""
}

func needGzipped(ctx context.Context) bool {
	val := ctx.Value(htx.ContextKeyRequestAcceptEncoding)
	enc, ok := val.(string)
	var gzipped = false
	if ok {
		encodings := strings.Split(strings.ToLower(enc), ",")
		for _, e := range encodings {
			if strings.TrimSpace(e) == "gzip" {
				gzipped = true
			}
		}
	}

	return gzipped
}
