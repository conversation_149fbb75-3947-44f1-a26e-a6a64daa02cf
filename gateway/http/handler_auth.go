package http

import (
	"net/http"

	"github.com/csee-pm/dashboard-pm/internal/auth"
	"github.com/csee-pm/dashboard-pm/internal/utils"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	htx "github.com/likearthian/apikit/transport/http"
)

type AuthAPIHandlers struct {
	AuthenticateHandler        http.Handler
	ValidateTokenHandler       http.Handler
	RefreshTokenHandler        http.Handler
	GetUserHandler             http.Handler
	GetUsernamesHandler        http.Handler
	AddUserHandler             http.Handler
	ChangePasswordHandler      http.Handler
	ResetUserPasswordHandler   http.Handler
	GoogleOAuthCallbackHandler http.Handler
}

func createAuthHandlers(authApi *auth.AuthAPI, options ...htx.ServerOption) AuthAPIHandlers {
	return AuthAPIHandlers{
		AuthenticateHandler: htx.NewServer(
			authApi.AuthenticateEndpoint,
			htx.CommonPostRequestDecoder[dto.AuthRequestDTO],
			CommonJSONResponseEncoder[dto.AuthResponseDTO],
			options...,
		),
		ValidateTokenHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.ValidateTokenEndpoint),
			htx.CommonGetRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		RefreshTokenHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.RefreshTokenEndpoint),
			htx.CommonGetRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		GetUserHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.GetUserEndpoint),
			htx.CommonGetRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		GetUsernamesHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.GetUsernamesEndpoint),
			htx.CommonGetRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		AddUserHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.AddUserEndpoint),
			htx.CommonPostRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		ChangePasswordHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.ChangePasswordEndpoint),
			htx.CommonPostRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		ResetUserPasswordHandler: htx.NewServer(
			utils.WithJWTAuthEPMiddleware(authApi.ResetUserPasswordEndpoint),
			htx.CommonPostRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
		GoogleOAuthCallbackHandler: htx.NewServer(
			authApi.GoogleOAuthCallbackEndpoint,
			htx.CommonGetRequestDecoder,
			CommonJSONResponseEncoder,
			options...,
		),
	}
}
