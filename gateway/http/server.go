package http

import (
	"fmt"
	"io"
	"io/fs"
	"mime"
	"net/http"
	"os"
	"path/filepath"

	"github.com/csee-pm/dashboard-pm/api"
	"github.com/csee-pm/dashboard-pm/internal/utils"
	"github.com/go-chi/chi/v5"
	md "github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	log "github.com/likearthian/apikit/logger"
	htx "github.com/likearthian/apikit/transport/http"
	"golang.org/x/time/rate"
)

var limiter = rate.NewLimiter(10, 20)

func rateLimit(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		if !limiter.Allow() {
			http.Error(w, http.StatusText(http.StatusTooManyRequests), http.StatusTooManyRequests)
			return
		}

		next.ServeHTTP(w, r)
	})
}

func NewHTTPServer(endpoints api.APIEndpoints, fe fs.FS, logger log.Logger) chi.Router {
	var allowHeader = []string{
		"Authorization",
		"Origin",
		"Accept",
		"Content-Type",
		"Accept-Encoding",
		"X-Requested-With",
		"Access-Control-Allow-Origin",
		"ngrok-skip-browser-warning",
	}
	var allowMethods = []string{http.MethodGet, http.MethodPost, http.MethodDelete, http.MethodOptions}
	allowCredential := false

	r := chi.NewRouter()
	r.Use(cors.Handler(cors.Options{
		// AllowedOrigins:   allowOrigin,
		AllowedMethods:   allowMethods,
		AllowedHeaders:   allowHeader,
		ExposedHeaders:   nil,
		AllowCredentials: allowCredential,
		//Debug:            true,
	}))

	r.Use(md.Recoverer)

	//r.Use(middleware.HttpRequestIDInjectorMiddleware)

	options := []htx.ServerOption{
		htx.ServerErrorEncoder(errorEncoder),
		// htx.ServerErrorHandler(transport.NewLogErrorHandler(errLogger)),
		htx.ServerErrorHandler(createHttpLogErrorHandler(logger)),
		htx.ServerBefore(htx.PopulateRequestContext, htx.ChiURLParamIntoContext),
		htx.ServerBefore(utils.JWTHTTPRequestToContext, utils.APIKeyRequestToContext),
	}

	version := os.Getenv("VERSION")
	if version == "" {
		version = "pong"
	}

	hostname, err := os.Hostname()
	if err != nil || hostname == "" {
		hostname = "localhost"
	}

	//httpLoggingMiddleware := middleware.MakeHttpTransportLoggingMiddleware(debugLogger)
	r.HandleFunc("/ping", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write([]byte(version))
	})

	r.With(md.Logger).With(md.RequestID).With(rateLimit).Mount("/api", createApiRoutes(endpoints.Auth, endpoints.Report, options...))

	r.Handle("/*", http.StripPrefix("/", CreateAssetHandler(fe)))

	return r
}

func CreateAssetHandler(assets fs.FS) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// fmt.Println("serve file:", r.URL.Path)
		err := tryRead(assets, r.URL.Path, w)
		if err == nil {
			return
		}

		err = tryRead(assets, "app.html", w)
		if err != nil {
			panic(err)
		}
	}
}

func tryRead(fs fs.FS, requestedPath string, w http.ResponseWriter) error {
	f, err := fs.Open(requestedPath)
	if err != nil {
		return err
	}
	defer f.Close()

	stat, _ := f.Stat()
	if stat.IsDir() {
		return fmt.Errorf("path is dir")
	}

	contentType := mime.TypeByExtension(filepath.Ext(requestedPath))
	w.Header().Set("Content-Type", contentType)
	_, err = io.Copy(w, f)
	return err
}
