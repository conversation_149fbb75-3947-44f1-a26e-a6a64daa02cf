package http

import (
	"net/http"

	"github.com/csee-pm/dashboard-pm/internal/auth"
	"github.com/csee-pm/dashboard-pm/internal/report"
	"github.com/go-chi/chi/v5"
	htx "github.com/likearthian/apikit/transport/http"
)

func createApiRoutes(authApi *auth.AuthAPI, reportApi *report.ReportAPI, options ...htx.ServerOption) http.Handler {
	// auth
	authHandler := createAuthHandlers(authApi, options...)

	// report
	reportHandler := CreateReportHandler(reportApi, options...)

	r := chi.NewRouter()

	r.Get("/report/{report_id}", reportHandler.GetReportHandler.ServeHTTP)

	r.Route("/auth", func(sr chi.Router) {
		sr.Method("POST", "/authenticate", authHandler.AuthenticateHandler)
		sr.Method("GET", "/validate", authHandler.ValidateTokenHandler)
		sr.Method("GET", "/refresh", authHandler.RefreshTokenHandler)
	})

	r.Method(http.MethodGet, "/google-oauth/callback", authHandler.GoogleOAuthCallbackHandler)

	r.Route("/idm", func(sr chi.Router) {
		sr.Method("GET", "/user", authHandler.GetUserHandler)
		sr.Method("POST", "/user", authHandler.AddUserHandler)
		sr.Method("POST", "/password", authHandler.ChangePasswordHandler)
		sr.Method("POST", "/reset-password", authHandler.ResetUserPasswordHandler)
		sr.Method("GET", "/usernames", authHandler.GetUsernamesHandler)
	})

	return r
}
