package comm_dash001

import (
	"fmt"
	"strings"
	"time"

	"cloud.google.com/go/civil"
	"github.com/csee-pm/dashboard-pm/internal/utils"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/enum"
)

type QueryField struct {
	Text  string
	Alias string
}

func (q QueryField) String() string {
	if q.Alias != "" {
		return fmt.Sprintf("%s %s", q.Text, q.Alias)
	}

	return q.Text
}

var kpiSumList = []string{
	"rev_total",
	"traffic_gb",
	"rgs_ga",
	"rgu30_gross_churn",
	"rgu30_churn_back",
	"rgu90_gross_churn",
	"rgu90_churn_back",
	"secondary_trad",
	"tertiary_trad",
}

var kpiSnapList = []string{
	"qsso",
	"quro",
	"sso",
	"uro",
	"vlr_daily",
	"rgu30_base",
	"rgu30_inflow",
	"rgu90_base",
	"rgu90_inflow",
}

func makeKpiNameSum(kpis []string) string {
	var bld strings.Builder
	for _, kpi := range kpis {
		if bld.Len() > 0 {
			bld.WriteString(",\n")
		}
		bld.WriteString(fmt.Sprintf("sum(case when kpi_name = '%s' then kpi_value else null end) %s", kpi, kpi))
	}

	return bld.String()
}

func makeKpiSum(kpis []string) string {
	var bld strings.Builder
	for _, kpi := range kpis {
		if bld.Len() > 0 {
			bld.WriteString(",\n")
		}
		bld.WriteString(fmt.Sprintf("sum(%s) %s", kpi, kpi))
	}

	return bld.String()
}

var mainTable = "`data-commstrexe-prd-565x.csee_pm.zz_regional_kpi_dly`"

func createQuery(req dto.GetReportRequestDTO) (string, map[string]any, error) {
	params, err := createQueryParams(req)
	if err != nil {
		return "", nil, err
	}

	kpiDly := makeKpiDly(req)
	maxDate := makeMaxDate("kpi_dly", req)
	kpiSum := makeKpiTiSum("kpi_dly", req)
	kpiSnap := makeKpiTiSnap("kpi_dly", "max_date", req)

	tiSelect := "a.dt"
	switch req.TiType {
	case enum.FullMonth, enum.MTD:
		tiSelect = "a.month_id"
		if req.TiType == enum.MTD {
			tiSelect += ", a.month_instance"
		}
	case enum.YTD:
		tiSelect = "a.year_id, a.year_instance"
	}

	idenFields := makeIdenFields(req)
	idenSelect := fmt.Sprintf("a.%s", strings.Join(utils.Map(idenFields, func(val QueryField) string { return val.String() }), ", a."))

	qryBld := strings.Builder{}
	// qryBld.WriteString(fmt.Sprintf("with kpi_dly as (\n%s\n),\n", kpiDly))
	qryBld.WriteString(fmt.Sprintf("with kpi_dly as (\n%s\n)", kpiDly))

	if req.TiType == enum.Daily {
		qryBld.WriteString(fmt.Sprintf("select %s, %s, a.%s, b.%s from kpi_dly a",
			tiSelect,
			idenSelect,
			strings.Join(kpiSumList, ", a."),
			strings.Join(kpiSnapList, ", a.")+","+makeRguSnapFormula("a")))

		return qryBld.String(), params, nil
	}

	qryBld.WriteString(",\n")
	qryBld.WriteString(fmt.Sprintf("max_date as (\n%s\n),\n", maxDate))
	qryBld.WriteString(fmt.Sprintf("kpi_sum as (\n%s\n),\n", kpiSum))
	qryBld.WriteString(fmt.Sprintf("kpi_snap as (\n%s\n)\n", kpiSnap))

	joinList := makeJoinList(req)

	qryBld.WriteString(fmt.Sprintf("select b.dt, %s, %s, a.%s, b.%s from kpi_sum a left join kpi_snap b on %s",
		tiSelect,
		idenSelect,
		strings.Join(kpiSumList, ", a."),
		strings.Join(kpiSnapList, ", b.")+","+makeRguSnapFormula("b"),
		strings.Join(joinList, " and ")))

	return qryBld.String(), params, nil
}

func makeRguSnapFormula(iden string) string {
	return fmt.Sprintf("%s.rgu30_base + %s.rgu30_inflow rgu_30, %s.rgu90_base + %s.rgu90_inflow rgu_90", iden, iden, iden, iden)
}

func createQueryParams(req dto.GetReportRequestDTO) (map[string]any, error) {
	params := make(map[string]any)
	dt_start := civil.Date{}
	dt_end := civil.Date{}

	switch req.TiType {
	case enum.Daily:
		var err error
		dt_start, err = civil.ParseDate(req.StartTime)
		if err != nil {
			return nil, err
		}

		dt_end, err = civil.ParseDate(req.EndTime)
		if err != nil {
			return nil, err
		}

	case enum.FullMonth:
		start, err := time.Parse("20060102", req.StartTime+"01")
		if err != nil {
			return nil, err
		}
		dt_start = civil.DateOf(start)

		end, err := time.Parse("20060102", req.EndTime+"01")
		if err != nil {
			return nil, err
		}
		dt_end = civil.DateOf(end.AddDate(0, 1, 0))

	case enum.MTD:
		var err error
		end, err := time.Parse("2006-01-02", req.EndTime)
		if err != nil {
			return nil, err
		}

		dt_end = civil.DateOf(end)
		mtd_day := end.Format("02")
		mtd := end.Format("200601")
		start := end.AddDate(0, -1, 0)
		dt_start, err = civil.ParseDate(start.Format("2006-01-") + "01")
		if err != nil {
			return nil, err
		}

		params["mtd"] = mtd
		params["mtd_day"] = mtd_day

	case enum.YTD:
		var err error
		end, err := time.Parse("2006-01-02", req.EndTime)
		if err != nil {
			return nil, err
		}

		dt_end = civil.DateOf(end)
		ytd := end.Format("2006")
		ytd_day := end.Format("0102")
		start := time.Date(end.Year()-1, 1, 1, 0, 0, 0, 0, time.Local)
		//lytd_date := civil.DateOf(time.Date(start.Year(), end.Month(), end.Day(), 0, 0, 0, 0, time.Local))
		dt_start, err = civil.ParseDate(start.Format("2006-01-") + "01")
		if err != nil {
			return nil, err
		}

		params["ytd"] = ytd
		params["ytd_day"] = ytd_day
	}

	params["dt_start"] = dt_start
	params["dt_end"] = dt_end

	if req.Entity > enum.IOH {
		params["entity"] = req.Entity.String()
	}

	return params, nil
}

func makeKpiDly(req dto.GetReportRequestDTO) string {
	bld := strings.Builder{}

	entField := "entity"
	if req.Entity == enum.IOH {
		entField = "'IOH' as entity"
	}

	bld.WriteString(fmt.Sprintf("select dt, circle, region, %s, %s from %s where dt >= ${dt_start}", entField, makeKpiNameSum(append(kpiSumList, kpiSnapList...)), mainTable))

	switch req.TiType {
	case enum.MTD:
		bld.WriteString(" and dt <= ${dt_end} and format_date('%d', dt) <= ${mtd_day}")
	case enum.YTD:
		bld.WriteString(" and dt <= ${dt_end} and format_date('%m%d', dt) <= ${ytd_day}")
	default:
		bld.WriteString(" and dt < ${dt_end}")
	}

	if req.Entity > enum.IOH {
		bld.WriteString(" and entity = ${entity}")
	}

	bld.WriteString(" group by dt, circle, region")
	if req.Entity > enum.IOH {
		bld.WriteString(", entity")
	}

	return bld.String()
}

func makeMaxDate(kpiDlyCteName string, req dto.GetReportRequestDTO) string {
	bld := strings.Builder{}
	tiSelect := "format_date('%Y%m', dt) month_id"
	if req.TiType == enum.YTD {
		tiSelect = "format_date('%Y', dt) year_id"
	}

	bld.WriteString(fmt.Sprintf("select %s, max(dt) maxDate from %s", tiSelect, kpiDlyCteName))

	bld.WriteString(" group by 1")

	return bld.String()
}

func makeKpiTiSum(kpiDlyCteName string, req dto.GetReportRequestDTO) string {
	tiFields := makeTiFields(req)
	tiSelect := strings.Join(utils.Map(tiFields, func(val QueryField) string { return val.String() }), ", ")

	idenFields := makeIdenFields(req)
	idenSelect := strings.Join(utils.Map(idenFields, func(val QueryField) string { return val.String() }), ", ")

	groupBy := strings.Join(utils.Map(append(tiFields, idenFields...), func(val QueryField) string { return val.Text }), ", ")

	return fmt.Sprintf("select %s, %s, %s from %s group by %s", tiSelect, idenSelect, makeKpiSum(kpiSumList), kpiDlyCteName, groupBy)
}

func makeKpiTiSnap(kpiDlyCteName string, maxDateCteName string, req dto.GetReportRequestDTO) string {
	tiFields := makeTiFields(req)
	tiSelect := strings.Join(utils.Map(tiFields, func(val QueryField) string { return val.String() }), ", ")

	idenFields := makeIdenFields(req)
	idenSelect := strings.Join(utils.Map(idenFields, func(val QueryField) string { return val.String() }), ", ")
	kpiSelect := strings.Join(kpiSnapList, ", ")
	groupBy := ""
	if req.Grouping < enum.Region {
		groupBy = strings.Join(utils.Map(append(tiFields, idenFields...), func(val QueryField) string { return val.Text }), ", ")
		kpiSelect = makeKpiSum(kpiSnapList)
	}

	qryBld := strings.Builder{}
	qryBld.WriteString(fmt.Sprintf("select a.dt, %s, %s, %s from %s a inner join %s b on a.dt = b.maxDate",
		tiSelect,
		idenSelect,
		kpiSelect,
		kpiDlyCteName,
		maxDateCteName))

	if groupBy != "" {
		qryBld.WriteString(fmt.Sprintf(" group by a.dt, %s", groupBy))
	}

	return qryBld.String()
}

func makeIdenFields(req dto.GetReportRequestDTO) []QueryField {
	switch req.Grouping {
	case enum.Circle:
		return []QueryField{{"circle", ""}, {"entity", ""}}
	case enum.Region:
		return []QueryField{{"circle", ""}, {"region", ""}, {"entity", ""}}
	}

	return []QueryField{{"entity", ""}}
}

func makeTiFields(req dto.GetReportRequestDTO) []QueryField {
	var tiSelect []QueryField
	switch req.TiType {
	case enum.FullMonth:
		tiSelect = []QueryField{{"format_date('%Y%m', dt)", "month_id"}}
	case enum.MTD:
		tiSelect = []QueryField{
			{"format_date('%Y%m', dt)", "month_id"},
			{"case when format_date('%Y%m', dt) = ${mtd} then 'MTD' else 'LMTD' end", "month_instance"},
		}
	case enum.YTD:
		tiSelect = []QueryField{
			{"format_date('%Y', dt)", "year_id"},
			{"case when format_date('%Y', dt) = ${ytd} then 'YTD' else 'LYTD' end", "year_instance"},
		}
	case enum.Daily:
		tiSelect = []QueryField{{"dt", ""}}
	}

	return tiSelect
}

func makeJoinList(req dto.GetReportRequestDTO) []string {
	var joinList []string
	idenFields := makeIdenFields(req)

	for _, iden := range idenFields {
		joinList = append(joinList, fmt.Sprintf("a.%s = b.%s", iden.Text, iden.Text))
	}

	switch req.TiType {
	case enum.Daily:
		joinList = append(joinList, "a.dt = b.dt")
	case enum.FullMonth, enum.MTD:
		joinList = append(joinList, "a.month_id = b.month_id")
	case enum.YTD:
		joinList = append(joinList, "a.year_id = b.year_id")
	}

	return joinList
}
