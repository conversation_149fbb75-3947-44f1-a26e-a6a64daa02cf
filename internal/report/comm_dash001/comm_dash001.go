package comm_dash001

import (
	"fmt"

	"cloud.google.com/go/bigquery"
	"github.com/csee-pm/dashboard-pm/internal/db/bq"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/enum"
)

func GetReport(db *bigquery.Client, req dto.GetReportRequestDTO) (any, error) {
	bu := req.BUType
	switch bu {
	case enum.Core:
		return GetCoreReport(db, req)
	case enum.Digital:
		return GetDigitalReport(db, req)
	case enum.Fintech:
		return GetFintechReport(db, req)
	}

	return nil, fmt.Errorf("invalid grouping: %s", bu)
}

func GetCoreReport(db *bigquery.Client, req dto.GetReportRequestDTO) (any, error) {
	qry, args, err := createQuery(req)
	if err != nil {
		return nil, err
	}

	// fmt.Printf("query: %s\n", qry)
	// fmt.Printf("args: %v\n", args)

	var res []BaseData
	if err := bq.BqSelect(db, &res, qry, bq.WithParams(args)); err != nil {
		return nil, fmt.Errorf("failed to get core report: %w", err)
	}

	return createKPISlice(res, req.Grouping, req.TiType), nil
}

func GetDigitalReport(db *bigquery.Client, req dto.GetReportRequestDTO) (any, error) {
	return nil, nil
}

func GetFintechReport(db *bigquery.Client, req dto.GetReportRequestDTO) (any, error) {
	return nil, nil
}
