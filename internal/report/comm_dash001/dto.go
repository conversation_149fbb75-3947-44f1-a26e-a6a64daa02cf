package comm_dash001

import (
	"time"

	"cloud.google.com/go/bigquery"
	"github.com/csee-pm/dashboard-pm/internal/utils"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/enum"
)

type CoreKPI struct {
	RevTotal        bigquery.NullFloat64 `json:"rev_total" bigquery:"rev_total"`
	TrafficGB       bigquery.NullFloat64 `json:"traffic_gb" bigquery:"traffic_gb"`
	VlrDaily        bigquery.NullFloat64 `json:"vlr_daily" bigquery:"vlr_daily"`
	RgsGA           bigquery.NullFloat64 `json:"rgs_ga" bigquery:"rgs_ga"`
	Rgu30           bigquery.NullFloat64 `json:"rgu_30" bigquery:"rgu_30"`
	Rgu30GrossChurn bigquery.NullFloat64 `json:"rgu_30_gross_churn" bigquery:"rgu_30_gross_churn"`
	Rgu30ChurnBack  bigquery.NullFloat64 `json:"rgu_30_churn_back" bigquery:"rgu_30_churn_back"`
	Rgu90           bigquery.NullFloat64 `json:"rgu_90" bigquery:"rgu_90"`
	Rgu90GrossChurn bigquery.NullFloat64 `json:"rgu_90_gross_churn" bigquery:"rgu_90_gross_churn"`
	Rgu90ChurnBack  bigquery.NullFloat64 `json:"rgu_90_churn_back" bigquery:"rgu_90_churn_back"`
	Qsso            bigquery.NullFloat64 `json:"qsso" bigquery:"qsso"`
	Sso             bigquery.NullFloat64 `json:"sso" bigquery:"sso"`
	Quro            bigquery.NullFloat64 `json:"quro" bigquery:"quro"`
	Uro             bigquery.NullFloat64 `json:"uro" bigquery:"uro"`
	SecondaryTrad   bigquery.NullFloat64 `json:"secondary_trad" bigquery:"secondary_trad"`
	TertiaryTrad    bigquery.NullFloat64 `json:"tertiary_trad" bigquery:"tertiary_trad"`
}

type BaseData struct {
	Dt            bigquery.NullDate   `json:"dt" bigquery:"dt"`
	MonthID       bigquery.NullString `json:"month_id" bigquery:"month_id"`
	MonthInstance bigquery.NullString `json:"month_instance" bigquery:"month_instance"`
	YearID        bigquery.NullString `json:"year_id" bigquery:"year_id"`
	YearInstance  bigquery.NullString `json:"year_instance" bigquery:"year_instance"`
	Circle        bigquery.NullString `json:"circle" bigquery:"circle"`
	Region        bigquery.NullString `json:"region" bigquery:"region"`
	EntityID      string              `json:"entity_id" bigquery:"entity"`
	CoreKPI
}

func createKPISlice(data []BaseData, grouping enum.Grouping, ti enum.TimeInstance) []dto.KPI[CoreKPI] {
	var kpis []dto.KPI[CoreKPI]
	for _, d := range data {
		timeInstance := dto.TimeInstance{
			TiType: ti,
		}

		switch ti {
		case enum.Daily:
			if d.Dt.Valid {
				timeInstance.Name = d.Dt.String()
				timeInstance.Value = d.Dt.Date.In(time.Local).UnixMilli()
			}

		case enum.FullMonth, enum.MTD:
			if d.MonthInstance.Valid {
				timeInstance.Name = d.MonthInstance.String()
			}

			tiTime, _ := time.Parse("200601", d.MonthID.String())
			if ti == enum.MTD {
				if d.Dt.Valid {
					tiTime = d.Dt.Date.In(time.Local)
				}
			}

			timeInstance.Value = tiTime.UnixMilli()
		case enum.YTD:
			if d.YearInstance.Valid {
				timeInstance.Name = d.YearInstance.String()
			}
			tiTime, _ := time.Parse("2006", d.YearID.String())
			if d.Dt.Valid {
				tiTime = d.Dt.Date.In(time.Local)
			}
			timeInstance.Value = tiTime.UnixMilli()
		}

		iden := dto.Identifier{
			Grouping: grouping,
			Circle:   utils.ToPtrOrNil(d.Circle.Valid, d.Circle.String()),
			Region:   utils.ToPtrOrNil(d.Region.Valid, d.Region.String()),
		}

		iden.Entity.UnmarshalText([]byte(d.EntityID))

		kpi := dto.KPI[CoreKPI]{
			TimeInstance: timeInstance,
			Identifier:   iden,
			KPI: CoreKPI{
				RevTotal:        d.RevTotal,
				TrafficGB:       d.TrafficGB,
				VlrDaily:        d.VlrDaily,
				RgsGA:           d.RgsGA,
				Rgu30:           d.Rgu30,
				Rgu30GrossChurn: d.Rgu30GrossChurn,
				Rgu30ChurnBack:  d.Rgu30ChurnBack,
				Rgu90:           d.Rgu90,
				Rgu90GrossChurn: d.Rgu90GrossChurn,
				Rgu90ChurnBack:  d.Rgu90ChurnBack,
				Qsso:            d.Qsso,
				Sso:             d.Sso,
				Quro:            d.Quro,
				Uro:             d.Uro,
				SecondaryTrad:   d.SecondaryTrad,
				TertiaryTrad:    d.TertiaryTrad,
			},
		}

		kpis = append(kpis, kpi)
	}

	return kpis
}
