package report

import (
	"context"
	"fmt"

	"cloud.google.com/go/bigquery"
	cd001 "github.com/csee-pm/dashboard-pm/internal/report/comm_dash001"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
)

type ReportAPI struct {
	db *bigquery.Client
}

func CreateReportAPI(db *bigquery.Client) ReportAPI {
	return ReportAPI{
		db: db,
	}
}

func (ra ReportAPI) GetReport(ctx context.Context, req dto.GetReportRequestDTO) (any, error) {
	switch req.ReportID {
	case "comm_dash001":
		return cd001.GetReport(ra.db, req)
	}

	return nil, fmt.Errorf("invalid report id: %s", req.ReportID)
}

func ValidateGetReportRequest(req dto.GetReportRequestDTO) error {

	return nil
}
