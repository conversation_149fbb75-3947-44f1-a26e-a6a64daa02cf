package utils

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	md "github.com/go-chi/chi/v5/middleware"
	htx "github.com/likearthian/apikit/transport/http"
)

type contextKey string

const (
	// JWTTokenContextKey holds the key used to store a JWT Token in the
	// context.
	JWTTokenContextKey contextKey = "JWTToken"

	// JWTClaimsContextKey holds the key used to store the JWT Claims in the
	// context.
	JWTClaimsContextKey contextKey = "JWTClaims"

	ApikeyContextKey contextKey = "api_key"
)

func JWTHTTPRequestToContext(ctx context.Context, r *http.Request) context.Context {
	token, ok := extractTokenFromAuthHeader(r.Header.Get("authorization"))
	if !ok {
		return ctx
	}

	return context.WithValue(ctx, JWTTokenContextKey, token)
}

func APIKeyRequestToContext(ctx context.Context, r *http.Request) context.Context {
	var keys []string
	for k, _ := range r.<PERSON>er {
		if strings.EqualFold("api_key", k) {
			keys = r.Header[k]
		}
	}

	if len(keys) == 0 {
		return ctx
	}

	return context.WithValue(ctx, ApikeyContextKey, keys[0])
}

func CheckAuthFromContext(ctx context.Context, checkAdmin bool) (*JwtClaims, error) {
	auth, ok := ctx.Value(JWTClaimsContextKey).(*JwtClaims)
	if !ok {
		return nil, fmt.Errorf("could not find auth context")
	}

	if !auth.IsAdmin && checkAdmin {
		return nil, fmt.Errorf("user unauthorized to do the action")
	}

	if strings.EqualFold(auth.Flag, "DISABLED") {
		return nil, fmt.Errorf("user is not active")
	}

	return auth, nil
}

func extractTokenFromAuthHeader(val string) (token string, ok bool) {
	authHeaderParts := strings.Split(val, " ")
	if len(authHeaderParts) != 2 || !strings.EqualFold(authHeaderParts[0], bearer) {
		return "", false
	}

	return authHeaderParts[1], true
}

func GetApikeyFromContext(ctx context.Context) string {
	apikey, ok := ctx.Value(ApikeyContextKey).(string)
	if !ok {
		return ""
	}

	return apikey
}

func GetBaseUrlFromContext(ctx context.Context) string {
	host, ok := ctx.Value(htx.ContextKeyRequestHost).(string)
	if !ok {
		return ""
	}

	proto, ok := ctx.Value(htx.ContextKeyRequestScheme).(string)
	if !ok {
		return ""
	}

	return proto + "://" + host
}

func ReqIDFromContext(ctx context.Context) (string, bool) {
	reqID, ok := ctx.Value(md.RequestIDKey).(string)
	if !ok {
		return "", false
	}
	return reqID, ok
}
