package utils

import "sync"

func ToPtr[T any](val T) *T {
	return &val
}

func ToPtrOrNil[T any](valid bool, val T) *T {
	if !valid {
		return nil
	}
	return &val
}

func MergeChan[T any](exit <-chan struct{}, cs ...<-chan T) chan T {
	var wg sync.WaitGroup
	out := make(chan T)

	// Start an output goroutine for each input channel in cs.  output
	// copies va=lues from c to out until c is closed, then calls wg.Done.
	output := func(c <-chan T) {
		defer func() {
			//fmt.Println("wg.Done()")
			wg.Done()
		}()
		for n := range c {
			select {
			case out <- n:
			case <-exit:
				return
			}
		}
	}
	wg.Add(len(cs))
	for _, c := range cs {
		go output(c)
	}

	// Start a goroutine to close out once all the output goroutines are
	// done.  This must start after the wg.Add call.
	go func() {
		wg.Wait()
		close(out)
	}()
	return out
}

func RunAsyncWithResult[T any](fn func() T) <-chan T {
	ch := make(chan T)
	go func() {
		defer close(ch)
		ch <- fn()
	}()
	return ch
}

func RunAsyncWithError(fn func() error) <-chan error {
	return RunAsyncWithResult(fn)
}
