package utils

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"github.com/csee-pm/dashboard-pm/pkg/errs"
	"github.com/dgrijalva/jwt-go/v4"
	"github.com/likearthian/apikit/api"
)

const (
	bearer       string = "bearer"
	bearerFormat string = "Bearer %s"
)

var jwtSigningMethod = jwt.SigningMethodHS256

// ClaimsFactory is a factory for jwt.Claims.
// Useful in NewParser middleware.
type ClaimsFactory func() jwt.Claims

// MapClaimsFactory is a ClaimsFactory that returns
// an empty jwt.MapClaims.
func MapClaimsFactory() jwt.Claims {
	return jwt.MapClaims{}
}

// StandardClaimsFactory is a ClaimsFactory that returns
// an empty jwt.StandardClaims.
func StandardClaimsFactory() jwt.Claims {
	return &jwt.StandardClaims{}
}

type JwtClaims struct {
	Username string
	IsAdmin  bool
	FullName string
	Email    string
	Flag     string
	Groups   []string
	jwt.StandardClaims
}

func CreateToken(issuer, username, fullname, email, flag string, isAdmin bool, groups []string, aud jwt.ClaimStrings, duration time.Duration) (string, error) {
	if len(encKeys) == 0 {
		return "", fmt.Errorf("no encryption key")
	}

	expiry := float64(time.Now().Add(duration).Unix())
	claims := JwtClaims{
		Username: username,
		Email:    email,
		IsAdmin:  isAdmin,
		FullName: fullname,
		Flag:     flag,
		Groups:   groups,
		StandardClaims: jwt.StandardClaims{
			ExpiresAt: jwt.NewTime(expiry),
			Issuer:    issuer,
			Audience:  aud,
		},
	}

	token := jwt.NewWithClaims(jwtSigningMethod, claims)
	source := rand.NewSource(time.Now().UnixNano())
	r := rand.New(source)
	n := r.Intn(len(encKeys) - 1)

	kid := strconv.Itoa(n)
	key, err := getKey(kid)
	if err != nil {
		return "", err
	}

	token.Header["kid"] = kid
	return token.SignedString(key)
}

func WithJWTAuthEPMiddleware[I, O any](ep api.Endpoint[I, O], options ...jwt.ParserOption) api.Endpoint[I, O] {
	return func(ctx context.Context, request I) (O, error) {
		var out O
		// tokenString is stored in the context from the transport handlers.
		tokenString, ok := ctx.Value(JWTTokenContextKey).(string)
		if !ok {
			return out, errs.ErrTokenContextMissing
		}

		options = append(options, jwt.WithAudience("ssp"))
		// Parse takes the token string and a function for looking up the
		// key. The latter is especially useful if you use multiple keys
		// for your application.  The standard is to use 'kid' in the head
		// of the token to identify which key to use, but the parsed token
		// (head and claims) is provided to the callback, providing
		// flexibility.
		token, err := jwt.ParseWithClaims(tokenString, &JwtClaims{}, func(token *jwt.Token) (interface{}, error) {
			// Don't forget to validate the alg is what you expect:
			if token.Method != jwtSigningMethod {
				return nil, errs.ErrUnexpectedSigningMethod
			}

			return JwtKeyGetterFunc(token)
		}, options...)

		if err != nil {
			switch err.(type) {
			case *jwt.MalformedTokenError:
				// Token is malformed
				return out, errs.ErrTokenMalformed
			case *jwt.TokenExpiredError:
				// Token is expired
				return out, errs.ErrTokenExpired
			case *jwt.TokenNotValidYetError:
				// Token is not active yet
				return out, errs.ErrTokenNotActive
			}
			// We have a ValidationError but have no specific Go kit error for it.
			// Fall through to return original error.
			return out, errs.ErrForbidden
		}

		if !token.Valid {
			return out, errs.ErrTokenInvalid
		}

		ctx = context.WithValue(ctx, JWTClaimsContextKey, token.Claims)
		// ctx = context.WithValue(ctx, JWTTokenContextKey, tokenString)

		return ep(ctx, request)
	}
}

func JwtKeyGetterFunc(token *jwt.Token) (interface{}, error) {
	if len(encKeys) == 0 {
		return nil, fmt.Errorf("no encryption key")
	}

	kid := token.Header["kid"].(string)
	return getKey(kid)
}

func getKey(kid string) ([]byte, error) {
	kidn, err := strconv.Atoi(kid)
	if err != nil {
		return nil, fmt.Errorf("invalid kid: %s", kid)
	}

	if kidn > len(encKeys)-1 {
		return nil, fmt.Errorf("invalid kid: %s", kid)
	}

	key := encKeys[kidn]
	return []byte(key), nil
}
