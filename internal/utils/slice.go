package utils

func Map[In any, Out any](slice []In, mapFunc func(val In) Out) []Out {
	var newSlice = make([]Out, len(slice))
	for i, val := range slice {
		newSlice[i] = mapFunc(val)
	}

	return newSlice
}

func Unique[T comparable](slice []T) []T {
	track := make(map[T]struct{})
	var newList = make([]T, 0, len(slice))
	for _, val := range slice {
		if _, ok := track[val]; !ok {
			track[val] = struct{}{}
			newList = append(newList, val)
		}
	}

	return newList
}

// SliceContains checks if an item is present in a slice.
// It returns true if the item is found, otherwise false.
func SliceContains[T comparable](slice []T, item T) bool {
	found := false
	for _, v := range slice {
		if v == item {
			found = true
			break
		}
	}

	return found
}

func Filter[T any](slice []T, filterFunc func(val T) bool) []T {
	var newSlice []T
	for i, val := range slice {
		if filterFunc(val) {
			newSlice = append(newSlice, slice[i])
		}
	}

	return newSlice
}

func ToMap[T any, K comparable, V any](slice []T, mapFn func(val T) (K, V)) map[K]V {
	var newMap = make(map[K]V)
	if len(slice) == 0 {
		return newMap
	}

	for _, val := range slice {
		k, v := mapFn(val)
		newMap[k] = v
	}

	return newMap
}

func SplitByChunk[T any](list []T, chunk int) [][]T {
	total := len(list)
	if total < chunk {
		return [][]T{list}
	}

	rem := total % chunk
	batch := total / chunk

	if rem > 0 {
		batch++
	}

	var newList = make([][]T, batch)
	start := 0
	end := chunk
	for i := 0; i < batch; i++ {
		if end > total {
			end = total
		}

		newList[i] = list[start:end]
		start += chunk
		end += chunk
	}

	return newList
}

func SplitByBatch[T any](list []T, batchnum int) [][]T {
	total := len(list)
	if total < batchnum {
		batchnum = total
	}

	rem := total % batchnum
	chunk := total / batchnum

	var newList = make([][]T, batchnum)
	start := 0
	end := chunk
	for i := 0; i < batchnum; i++ {
		if rem > 0 {
			end++
		}

		newList[i] = list[start:end]
		start = end
		end += chunk
		rem--
	}

	return newList
}
