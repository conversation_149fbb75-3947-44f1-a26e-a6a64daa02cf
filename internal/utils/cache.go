package utils

import (
	"fmt"
	"sync"
)

type CacheOption[K comparable, V any] func(*SimpleCache[K, V])

func WithGetterFn[K comparable, V any](getter func(K) (V, error)) CacheOption[K, V] {
	return func(sc *SimpleCache[K, V]) {
		sc.getterFn = getter
	}
}

func WithAfterSetFn[K comparable, V any](setter func(K, V)) CacheOption[K, V] {
	return func(sc *SimpleCache[K, V]) {
		sc.afterSetFn = setter
	}
}

type SimpleCache[K comparable, V any] struct {
	sync.RWMutex
	cache      map[K]V
	getterFn   func(K) (V, error)
	afterSetFn func(K, V)
}

func NewCache[K comparable, V any](options ...CacheOption[K, V]) *SimpleCache[K, V] {
	cache := &SimpleCache[K, V]{
		cache: make(map[K]V),
	}

	for _, option := range options {
		option(cache)
	}

	return cache
}

func (sc *SimpleCache[K, V]) Set(key K, value V) {
	sc.Lock()
	defer sc.Unlock()

	sc.cache[key] = value
	if sc.afterSetFn != nil {
		sc.afterSetFn(key, value)
	}
}

func (sc *SimpleCache[K, V]) Get(key K) (V, error) {
	sc.RLock()

	defer sc.RUnlock()
	item, ok := sc.cache[key]
	if !ok {
		if sc.getterFn != nil {
			return sc.getterFn(key)
		}
		return item, fmt.Errorf("key %v not found", key)
	}

	return item, nil
}

func (sc *SimpleCache[K, V]) Delete(key K) {
	sc.Lock()
	defer sc.Unlock()
	delete(sc.cache, key)
}

func (sc *SimpleCache[K, V]) Clear() {
	sc.Lock()
	defer sc.Unlock()
	sc.cache = make(map[K]V)
}

func (sc *SimpleCache[K, V]) Len() int {
	sc.RLock()
	defer sc.RUnlock()
	return len(sc.cache)
}
