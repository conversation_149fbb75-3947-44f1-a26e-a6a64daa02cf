package bq

import (
	"context"
	"fmt"
	"strings"

	"cloud.google.com/go/bigquery"
	"google.golang.org/api/iterator"
)

type dbOptions struct {
	params map[string]any
	ctx    context.Context
}

type DBOption func(*dbOptions)

func WithParams(params map[string]any) DBOption {
	return func(opts *dbOptions) {
		opts.params = params
	}
}

func WithContext(ctx context.Context) DBOption {
	return func(opts *dbOptions) {
		opts.ctx = ctx
	}
}

func BqInjectQueryParams(qry string, params map[string]any) (string, error) {
	for k := range params {
		key := fmt.Sprintf("${%s}", k)
		repl := fmt.Sprintf("@%s", k)
		qry = strings.ReplaceAll(qry, key, repl)
	}

	return qry, nil
}

func BqCreateQuery(bq *bigquery.Client, qry string, params map[string]any) (*bigquery.Query, error) {
	qry, err := BqInjectQueryParams(qry, params)
	if err != nil {
		return nil, err
	}

	bquery := bq.Query(qry)
	if len(params) > 0 {
		var bparams []bigquery.QueryParameter
		for k, v := range params {
			bparams = append(bparams, bigquery.QueryParameter{Name: k, Value: v})
		}
		bquery.Parameters = bparams
	}

	return bquery, nil
}

func BqSelect[T any](bq *bigquery.Client, dest *[]T, qry string, options ...DBOption) error {
	opt := dbOptions{ctx: context.Background()}

	for _, o := range options {
		o(&opt)
	}

	if opt.ctx == nil {
		opt.ctx = context.Background()
	}

	query := bq.Query(qry)
	var err error
	if opt.params != nil {
		query, err = BqCreateQuery(bq, qry, opt.params)
		if err != nil {
			return err
		}
	}

	iter, err := query.Read(opt.ctx)
	if err != nil {
		return fmt.Errorf("failed to read query result: %w", err)
	}

	var columns []string
	for {
		var row T
		err := iter.Next(&row)
		if err == iterator.Done {
			break
		}
		if err != nil {
			return err
		}

		if len(columns) == 0 {
			for _, col := range iter.Schema {
				columns = append(columns, col.Name)
			}
		}

		*dest = append(*dest, row)
	}

	return nil
}
