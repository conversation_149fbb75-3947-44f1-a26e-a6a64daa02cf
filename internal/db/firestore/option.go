package firestore

import (
	"cloud.google.com/go/firestore"
)

type queryOptions struct {
	orderBy []Sort
	limit   uint
	offset  uint
	filters []firestore.EntityFilter
}

type QueryOption func(*queryOptions)

func WithOrderBy(sorts ...Sort) QueryOption {
	return func(o *queryOptions) {
		o.orderBy = sorts
	}
}

func WithLimit(limit uint) QueryOption {
	return func(o *queryOptions) {
		o.limit = limit
	}
}

func WithOffset(offset uint) QueryOption {
	return func(o *queryOptions) {
		o.offset = offset
	}
}

func WithFilters(filters ...firestore.EntityFilter) QueryOption {
	return func(o *queryOptions) {
		o.filters = append(o.filters, filters...)
	}
}
