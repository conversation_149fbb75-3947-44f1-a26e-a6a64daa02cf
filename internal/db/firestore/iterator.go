package firestore

import (
	"cloud.google.com/go/firestore"
	"google.golang.org/api/iterator"
)

var Done = iterator.Done

// DocIterator is a generic iterator for Firestore documents.
type DocIterator[T IDSetter] struct {
	iter *firestore.DocumentIterator
}

// Next retrieves the next document from the iterator and populates the provided struct with the document data.
// It returns an error if there was an error retrieving the document.
func (d *DocIterator[T]) Next() (T, error) {
	var data T
	doc, err := d.iter.Next()
	if err != nil {
		if err == iterator.Done {
			return data, Done
		}
		return data, err
	}

	doc.DataTo(&data)
	data.SetID(doc.Ref.ID)
	return data, nil
}

// Stop stops the iterator.
func (d *DocIterator[T]) Stop() {
	d.iter.Stop()
}
