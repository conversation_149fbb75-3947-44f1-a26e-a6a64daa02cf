package firestore

var sortOrders = []string{"asc", "desc"}

type SortOrder int

const (
	Asc SortOrder = iota
	Desc
)

func (o SortOrder) String() string {
	if o < 0 || int(o) >= len(sortOrders) {
		return ""
	}

	return sortOrders[o]
}

func (o SortOrder) IsValid() bool {
	return o >= 0 && int(o) < len(sortOrders)
}

func NewSort(field string, order SortOrder) Sort {
	return Sort{Field: field, Order: order, valid: true}
}

func NewSortFromString(sort string) Sort {
	var order SortOrder
	valid := true
	sign := sort[0]
	field := sort[1:]
	switch sign {
	case '+':
		order = Asc
	case '-':
		order = Desc
	default:
		order = Asc
		field = sort
	}

	// if sort == "desc" {
	// 	order = Desc
	// }

	return Sort{Field: field, Order: order, valid: valid}
}

type Sort struct {
	valid bool
	Field string
	Order SortOrder
}

func (s Sort) IsValid() bool {
	return s.Order.IsValid() && s.valid
}
