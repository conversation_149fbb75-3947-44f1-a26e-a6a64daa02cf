package firestore

import (
	"context"
	"fmt"

	"cloud.google.com/go/firestore"
	"github.com/csee-pm/dashboard-pm/pkg/errs"
	"google.golang.org/api/iterator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

type IDSetter interface {
	SetID(string)
}

// FirestoreRepository is a generic repository implementation for Firestore.
type FirestoreRepository[T IDSetter] struct {
	colName string
	client  *firestore.Client
}

// CreateFirestoreRepository creates a new instance of FirestoreRepository with the provided Firestore client and collection name.
func CreateFirestoreRepository[T IDSetter](client *firestore.Client, colName string) FirestoreRepository[T] {
	repo := FirestoreRepository[T]{colName: colName, client: client}
	// if err := repo.initRepo(); err != nil {
	// 	panic(err)
	// }

	return repo
}

// Iterator returns a new DocIterator for iterating over documents in the Firestore collection.
func (r FirestoreRepository[T]) Iterator(ctx context.Context, options ...QueryOption) *DocIterator[T] {
	opt := queryOptions{}
	for _, option := range options {
		option(&opt)
	}

	qry := r.client.Collection(r.colName).Query
	for _, filter := range opt.filters {
		qry = qry.WhereEntity(filter)
	}

	if opt.limit > 0 {
		qry = qry.Limit(int(opt.limit))
	}

	if opt.offset > 0 {
		qry = qry.Offset(int(opt.offset))
	}

	if len(opt.orderBy) > 0 {
		for _, sort := range opt.orderBy {
			var dir firestore.Direction
			switch sort.Order {
			case Asc:
				dir = firestore.Asc
			case Desc:
				dir = firestore.Desc
			}

			qry = qry.OrderBy(sort.Field, dir)
		}
	}

	iter := qry.Documents(ctx)
	return &DocIterator[T]{iter: iter}
}

// Get retrieves a document from Firestore using the provided key and populates the provided struct with the document data.
// It returns an error if the document does not exist or if there was an error retrieving the document.
func (r FirestoreRepository[T]) Get(ctx context.Context, key string, dest T) error {
	col := r.client.Collection(r.colName)
	snap, err := col.Doc(key).Get(ctx)
	if err != nil && status.Code(err) != codes.NotFound {
		return err
	}

	if snap.Exists() {
		err = snap.DataTo(dest)
		dest.SetID(snap.Ref.ID)
		return err
	}

	return errs.ErrKeynotFound
}

// Select retrieves documents from the Firestore collection and populates the provided destination slice with the data.
// It accepts optional query options to filter the documents.
// The retrieved documents are decoded into the type T and appended to the destination slice.
// The function returns an error if there was a problem retrieving the documents.
func (r FirestoreRepository[T]) Select(ctx context.Context, dest *[]T, options ...QueryOption) error {
	opt := queryOptions{}
	for _, option := range options {
		option(&opt)
	}

	qry := r.client.Collection(r.colName).Query
	for _, filter := range opt.filters {
		qry = qry.WhereEntity(filter)
	}

	if opt.limit > 0 {
		qry = qry.Limit(int(opt.limit))
	}

	if opt.offset > 0 {
		qry = qry.Offset(int(opt.offset))
	}

	if len(opt.orderBy) > 0 {
		for _, sort := range opt.orderBy {
			var dir firestore.Direction
			switch sort.Order {
			case Asc:
				dir = firestore.Asc
			case Desc:
				dir = firestore.Desc
			}
			fmt.Printf("Order by %s %v\n", sort.Field, dir)
			qry = qry.OrderBy(sort.Field, dir)
		}
	}

	iter := qry.Documents(ctx)
	defer iter.Stop()
	for {
		doc, err := iter.Next()
		if err != nil {
			if err != iterator.Done {
				fmt.Printf("ERROR SELECT: %s", err)
			}
			break
		}

		var data T
		doc.DataTo(&data)
		data.SetID(doc.Ref.ID)
		*dest = append(*dest, data)
	}

	return nil
}

func (r FirestoreRepository[T]) Count(ctx context.Context, options ...QueryOption) (int, error) {
	opt := queryOptions{}
	for _, option := range options {
		option(&opt)
	}

	qry := r.client.Collection(r.colName).Query
	for _, filter := range opt.filters {
		qry = qry.WhereEntity(filter)
	}

	iter := qry.Documents(ctx)
	defer iter.Stop()
	count := 0
	for {
		_, err := iter.Next()
		if err != nil {
			break
		}

		count++
	}

	return count, nil
}

// Add adds a new document to Firestore with the provided key and data.
// It returns an error if there was an error creating the document.
func (r FirestoreRepository[T]) Add(ctx context.Context, key string, data T) error {
	col := r.client.Collection(r.colName)
	_, err := col.Doc(key).Create(ctx, data)
	return err
}

// Update updates an existing document in Firestore with the provided key and data.
// It returns an error if there was an error updating the document.
func (r FirestoreRepository[T]) Update(ctx context.Context, key string, data T) error {
	col := r.client.Collection(r.colName)
	_, err := col.Doc(key).Set(ctx, data)
	return err
}

func (r FirestoreRepository[T]) Delete(ctx context.Context, key string) error {
	col := r.client.Collection(r.colName)
	_, err := col.Doc(key).Delete(ctx)
	return err
}

func (r FirestoreRepository[T]) GetCollection() *firestore.CollectionRef {
	return r.client.Collection(r.colName)
}
