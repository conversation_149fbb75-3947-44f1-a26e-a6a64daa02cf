package db

import "context"

type Params map[string]*ParamValue
type ParamValue struct {
	Name  string
	Value any
	Pos   []int
}

func NewParam(name string, value any) *ParamValue {
	return &ParamValue{Name: name, Value: value}
}

type dbOptions struct {
	params Params
	ctx    context.Context
}

type DBOption func(*dbOptions)

func WithParams(params Params) DBOption {
	return func(opts *dbOptions) {
		opts.params = params
	}
}

func WithContext(ctx context.Context) DBOption {
	return func(opts *dbOptions) {
		opts.ctx = ctx
	}
}

type DB interface {
	Query(qry string, options ...DBOption) (Iterator, error)
	Exec(qry string, options ...DBOption) error
}
