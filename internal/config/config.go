package config

import (
	"strings"

	"github.com/spf13/viper"
)

type Config struct {
	viper.Viper
}

var listenPort = 8080

func CreateConfig() *Config {
	conf := Config{*viper.New()}
	conf.SetConfigName("dashboard-pm.conf")
	conf.SetConfigType("yaml")
	conf.AddConfigPath(".")
	conf.SetEnvPrefix("CSEE")
	conf.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	conf.AutomaticEnv()
	conf.SetDefault("server.port", listenPort)
	conf.ReadInConfig()

	return &conf
}

type ServerConfig struct {
	Port     int
	LogLevel int
}

type GCloudConfig struct {
	ProjectID string
}

type BQConfig struct {
	ProjectID string
	JsonKey   string
}

type AtlasConfig struct {
	User     string
	Password string
}

type FirestoreConfig struct {
	ProjectID string
	JsonKey   string
}

func (c *Config) GetServerConfig() ServerConfig {
	return ServerConfig{
		Port:     c.GetInt("server.port"),
		LogLevel: c.<PERSON>nt("server.log_level"),
	}
}

func (c *Config) GetGCloudConfig() GCloudConfig {
	return GCloudConfig{
		ProjectID: c.GetString("gcloud.project_id"),
	}
}

func (c *Config) GetAtlasConfig() AtlasConfig {
	return AtlasConfig{
		User:     c.GetString("atlas.user"),
		Password: c.GetString("atlas.password"),
	}
}

func (c *Config) GetFirestoreConfig() FirestoreConfig {
	return FirestoreConfig{
		ProjectID: c.GetString("firestore.project_id"),
		JsonKey:   c.GetString("firestore.json_key"),
	}
}

func (c *Config) GetBQConfig() BQConfig {
	return BQConfig{
		ProjectID: c.GetString("bq.project_id"),
		JsonKey:   c.GetString("bq.json_key"),
	}
}
