package auth

import (
	"context"
	"errors"

	"cloud.google.com/go/firestore"
	fstore "github.com/csee-pm/dashboard-pm/internal/db/firestore"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/errs"
	"gopkg.in/guregu/null.v4"
)

type GoogleOAuthUser struct {
	ID          string      `json:"id" bson:"_id" firestore:"-"`
	Email       string      `json:"email" bson:"email" firestore:"email"`
	Fullname    string      `json:"fullname" bson:"fullname" firestore:"fullname"`
	Picture     string      `json:"picture,omitempty" bson:"picture" firestore:"picture"`
	IsAdmin     bool        `json:"isAdmin" bson:"isAdmin" firestore:"isAdmin"`
	AccessToken null.String `json:"access_token" bson:"access_token" firestore:"access_token"`
	Expiry      null.Int    `json:"expiry" bson:"expiry" firestore:"expiry"`
}

func (gu *GoogleOAuthUser) SetID(id string) {
	gu.ID = id
}

func (gu *GoogleOAuthUser) ToDTO() dto.UserDTO {
	return dto.UserDTO{
		Email:    null.StringFrom(gu.Email),
		Fullname: gu.Fullname,
		IsAdmin:  null.BoolFrom(gu.IsAdmin),
	}
}

type GoogleOAuthRepository struct {
	fstore.FirestoreRepository[*GoogleOAuthUser]
}

func CreateGoogleOAuthRepository(client *firestore.Client) GoogleOAuthRepository {
	repo := fstore.CreateFirestoreRepository[*GoogleOAuthUser](client, "bpcs/bpcspm/google_oauth")
	gOAuthRepo := GoogleOAuthRepository{repo}

	if err := gOAuthRepo.initRepo(); err != nil {
		panic(err)
	}

	return gOAuthRepo
}

func (gur GoogleOAuthRepository) initRepo() error {
	for _, defUser := range defaultGoogleOAuthUsers {
		var user GoogleOAuthUser
		err := gur.Get(context.Background(), defUser.ID, &user)
		if err != nil && !errors.Is(err, errs.ErrKeynotFound) {
			return err
		}

		if errors.Is(err, errs.ErrKeynotFound) {
			newUser := &defUser

			if err := gur.Add(context.Background(), defUser.ID, newUser); err != nil {
				return err
			}
		}
	}

	return nil
}

var defaultGoogleOAuthUsers = []GoogleOAuthUser{
	{
		ID:       "<EMAIL>",
		Fullname: "Ziska Zarkasyi",
		Email:    "<EMAIL>",
		IsAdmin:  true,
	},
	{
		ID:       "<EMAIL>",
		Fullname: "Ziska Zarkasyi",
		Email:    "<EMAIL>",
		IsAdmin:  false,
	},
}
