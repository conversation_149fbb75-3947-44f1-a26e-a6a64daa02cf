package auth

import (
	"context"
	"errors"

	"cloud.google.com/go/firestore"
	fstore "github.com/csee-pm/dashboard-pm/internal/db/firestore"
	"github.com/csee-pm/dashboard-pm/internal/utils"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/errs"
	"gopkg.in/guregu/null.v4"
)

var UserStatusFlag = []string{
	"ACTIVE",
	"DISABLED",
}

type User struct {
	ID         string   `json:"id" bson:"_id" firestore:"-"`
	Password   string   `json:"password" bson:"password" firestore:"password"`
	Fullname   string   `json:"fullname" bson:"fullname" firestore:"fullname"`
	Email      string   `json:"email" bson:"email" firestore:"email"`
	IsAdmin    bool     `json:"isAdmin" bson:"isAdmin" firestore:"isAdmin"`
	StatusFlag string   `json:"status_flag" bson:"status_flag" firestore:"status_flag"`
	Groups     []string `json:"groups" bson:"groups" firestore:"groups"`
}

func (u *User) SetID(id string) {
	u.ID = id
}

func (u *User) ToDTO() dto.UserDTO {
	return dto.UserDTO{
		Username:   u.ID,
		Fullname:   u.Fullname,
		Email:      null.StringFrom(u.Email),
		IsAdmin:    null.BoolFrom(u.IsAdmin),
		StatusFlag: null.StringFrom(u.StatusFlag),
		Groups:     u.Groups,
	}
}

type UserRepository struct {
	fstore.FirestoreRepository[*User]
}

func CreateUserRepository(client *firestore.Client, useTestData bool) UserRepository {
	repo := fstore.CreateFirestoreRepository[*User](client, "bpcs/bpcspm/user")
	userRepo := UserRepository{repo}
	if err := userRepo.initRepo(); err != nil {
		panic(err)
	}

	if useTestData {
		// if err := userRepo.setTestData(); err != nil {
		// 	panic(err)
		// }
	}

	return userRepo
}

// func (ur UserRepository) setTestData() error {
// 	users := getMockUserData()

// 	ctx := context.Background()
// 	for _, user := range users {
// 		if err := ur.Add(ctx, user.ID, &user); err != nil {
// 			return err
// 		}
// 	}

// 	return nil
// }

func (ur UserRepository) GetAll(ctx context.Context) ([]User, error) {
	var users []User

	iter := ur.Iterator(ctx)
	defer iter.Stop()
	for {
		doc, err := iter.Next()
		if err != nil {
			break
		}

		users = append(users, *doc)
	}

	return users, nil
}

func (ur UserRepository) initRepo() error {
	for _, defUser := range defaultUsers {
		var user User
		err := ur.Get(context.Background(), defUser.ID, &user)
		if err != nil && !errors.Is(err, errs.ErrKeynotFound) {
			return err
		}

		if errors.Is(err, errs.ErrKeynotFound) {
			newUser := &defUser

			if err := ur.Add(context.Background(), defUser.ID, newUser); err != nil {
				return err
			}
		}
	}

	return nil
}

var defaultUsers = []User{
	{
		ID:         "80208379",
		Fullname:   "Ziska Zarkasyi",
		Email:      "<EMAIL>",
		IsAdmin:    true,
		Password:   utils.PasswordHash("Aldanista@2201"),
		StatusFlag: "ACTIVE",
	},
	{
		ID:         "bpcs_user",
		Fullname:   "BPCS Perf Management",
		Email:      "<EMAIL>",
		IsAdmin:    false,
		Password:   utils.PasswordHash("Indosat@2025"),
		StatusFlag: "ACTIVE",
	},
}
