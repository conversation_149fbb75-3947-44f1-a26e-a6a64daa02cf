package auth

import (
	"context"
	"errors"
	"fmt"
	"time"

	"cloud.google.com/go/firestore"
	"github.com/csee-pm/dashboard-pm/internal/config"
	"github.com/csee-pm/dashboard-pm/internal/utils"
	"github.com/csee-pm/dashboard-pm/pkg/dto"
	"github.com/csee-pm/dashboard-pm/pkg/errs"
	"golang.org/x/oauth2"
	googleOAuth2 "google.golang.org/api/oauth2/v2"
	"google.golang.org/api/option"
	"gopkg.in/guregu/null.v4"
)

type AuthAPI struct {
	userRepo   UserRepository
	gOAuthRepo GoogleOAuthRepository
	userCache  *utils.SimpleCache[string, dto.UserDTO]
}

func CreateAuthAPI(client *firestore.Client, conf *config.Config) (*AuthAPI, error) {
	useTestData := conf.GetBool("use_test_data")
	authAPI := &AuthAPI{
		userRepo:   CreateUserRepository(client, useTestData),
		gOAuthRepo: CreateGoogleOAuthRepository(client),
	}

	userCache, err := authAPI.initUserCache(context.Background())
	if err != nil {
		return nil, err
	}

	authAPI.userCache = userCache
	populateEncKeys(conf, 15)
	return authAPI, nil
}

func populateEncKeys(conf *config.Config, n int) {
	for i := 0; i < n; i++ {
		encKey := conf.GetString(fmt.Sprintf("enc.key_%d", i))
		if len(encKey) == 32 {
			utils.AddEncKey(encKey)
		}
	}
}

func (a *AuthAPI) AuthenticateEndpoint(ctx context.Context, req dto.AuthRequestDTO) (dto.AuthResponseDTO, error) {
	var response dto.AuthResponseDTO
	var user User
	err := a.userRepo.Get(ctx, req.Username, &user)
	if err != nil && !errors.Is(err, errs.ErrKeynotFound) {
		return response, err
	}

	if errors.Is(err, errs.ErrKeynotFound) {
		return response, errs.ErrInvalidUserPassword
	}

	if user.StatusFlag == "DISABLED" {
		return response, errs.ErrInvalidUserPassword
	}

	if err := utils.PasswordCompare(user.Password, req.Password); err != nil {
		return response, errs.ErrInvalidUserPassword
	}

	expiryDuration := 24 * time.Hour
	expiry := time.Now().Add(expiryDuration).UnixMilli()

	token, err := a.createToken(user)
	if err != nil {
		return response, err
	}

	fullName := user.Fullname
	if fullName == "" {
		fullName = user.ID
	}

	return dto.AuthResponseDTO{
		Token:      token,
		Fullname:   fullName,
		Username:   user.ID,
		Email:      user.Email,
		IsAdmin:    user.IsAdmin,
		StatusFlag: user.StatusFlag,
		Expiry:     expiry,
		Groups:     user.Groups,
	}, nil
}

func (a *AuthAPI) ValidateTokenEndpoint(ctx context.Context, req any) (*dto.AuthResponseDTO, error) {
	claims, err := utils.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	token := ctx.Value(utils.JWTTokenContextKey).(string)

	var user User
	if err := a.userRepo.Get(ctx, claims.Username, &user); err != nil {
		return nil, err
	}

	auth := &dto.AuthResponseDTO{
		Username:   claims.Username,
		Fullname:   user.Fullname,
		Email:      user.Email,
		Expiry:     claims.ExpiresAt.UnixMilli(),
		IsAdmin:    user.IsAdmin,
		StatusFlag: user.StatusFlag,
		Groups:     user.Groups,
		Token:      token,
	}

	return auth, nil
}

func (a *AuthAPI) RefreshTokenEndpoint(ctx context.Context, req any) (*dto.AuthResponseDTO, error) {
	claims, err := utils.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	expiryDuration := 24 * time.Hour
	expiry := time.Now().Add(expiryDuration).UnixMilli()

	newToken, err := utils.CreateToken("bpcs", claims.Username, claims.FullName, claims.Email, claims.Flag, claims.IsAdmin, claims.Groups, []string{"ssp"}, expiryDuration)
	if err != nil {
		return nil, err
	}

	auth := &dto.AuthResponseDTO{
		Username:   claims.Username,
		Fullname:   claims.FullName,
		Email:      claims.Email,
		Expiry:     expiry,
		IsAdmin:    claims.IsAdmin,
		StatusFlag: claims.Flag,
		Token:      newToken,
	}

	return auth, nil
}

func (a *AuthAPI) GoogleOAuthCallbackEndpoint(ctx context.Context, req dto.GoogleOAuthCallbackDTO) (*dto.AuthResponseDTO, error) {
	accessToken := oauth2.Token{AccessToken: req.AccessToken}
	oauth2Service, err := googleOAuth2.NewService(ctx, option.WithTokenSource(oauth2.StaticTokenSource(&accessToken)))
	if err != nil {
		return nil, err
	}

	userInfo, err := oauth2Service.Userinfo.Get().Do()
	if err != nil {
		return nil, err
	}

	fmt.Printf("userInfo: %+v\n", userInfo)

	var user GoogleOAuthUser
	if err := a.gOAuthRepo.Get(ctx, userInfo.Email, &user); err != nil {
		return nil, err
	}

	if errors.Is(err, errs.ErrKeynotFound) {
		return nil, errs.ErrInvalidUserPassword
	}

	user.AccessToken = null.StringFrom(req.AccessToken)
	user.Expiry = null.IntFrom(int64(req.ExpireIn))

	if err := a.gOAuthRepo.Update(ctx, user.ID, &user); err != nil {
		return nil, err
	}

	expiryDuration := 24 * time.Hour
	expiry := time.Now().Add(expiryDuration).UnixMilli()

	token, err := a.createToken(User{
		ID:       user.ID,
		IsAdmin:  user.IsAdmin,
		Email:    user.Email,
		Fullname: user.Fullname,
	})

	if err != nil {
		return nil, err
	}

	fullName := user.Fullname
	if fullName == "" {
		fullName = user.ID
	}

	return &dto.AuthResponseDTO{
		Token:      token,
		Fullname:   fullName,
		Username:   user.ID,
		Email:      user.Email,
		StatusFlag: "ACTIVE",
		IsAdmin:    user.IsAdmin,
		Expiry:     expiry,
	}, nil
}

func (a *AuthAPI) GetUserEndpoint(ctx context.Context, req dto.GetUserRequestDTO) ([]dto.UserDTO, error) {
	claims, err := utils.CheckAuthFromContext(ctx, false)
	if err != nil {
		return nil, err
	}

	var users []dto.UserDTO
	if req.Username.ValueOrZero() == "" {
		if !req.Subordinates.ValueOrZero() || claims.IsAdmin {
			users, err = a.getAllUsers(ctx)
			if err != nil {
				return nil, err
			}
		}

		return censorUsers(users, claims.IsAdmin), nil
	}

	user, err := a.userCache.Get(req.Username.String)
	if err != nil {
		return nil, err
	}

	return censorUsers([]dto.UserDTO{user}, claims.IsAdmin), nil
}

func censorUsers(users []dto.UserDTO, isAdmin bool) []dto.UserDTO {
	if isAdmin {
		return users
	}

	return utils.Map(users, func(val dto.UserDTO) dto.UserDTO {
		return dto.UserDTO{
			Username: val.Username,
			Fullname: val.Fullname,
			ReportTo: val.ReportTo,
			Groups:   val.Groups,
		}
	})
}

func (a *AuthAPI) GetUserCache() *utils.SimpleCache[string, dto.UserDTO] {
	return a.userCache
}

func (a *AuthAPI) getAllUsers(ctx context.Context) ([]dto.UserDTO, error) {
	users, err := a.userRepo.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	return utils.Map(users, func(val User) dto.UserDTO {
		return val.ToDTO()
	}), nil
}

func (a *AuthAPI) GetUsernamesEndpoint(ctx context.Context, req any) ([]dto.UserDTO, error) {
	users, err := a.userRepo.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	return utils.Map(users, func(val User) dto.UserDTO {
		return dto.UserDTO{
			Username: val.ID,
			Fullname: val.Fullname,
		}
	}), nil
}

func (a *AuthAPI) AddUserEndpoint(ctx context.Context, req dto.PostUserRequestDTO) (dto.UserDTO, error) {
	_, err := utils.CheckAuthFromContext(ctx, true)
	if err != nil {
		return dto.UserDTO{}, err
	}

	if err := a.validatePostUserRequest(ctx, req); err != nil {
		return dto.UserDTO{}, err
	}

	fullName := req.Fullname
	if fullName == "" {
		fullName = req.Username
	}

	user := &User{
		ID:         req.Username,
		IsAdmin:    req.IsAdmin,
		Email:      req.Email,
		Fullname:   fullName,
		Password:   utils.PasswordHash(req.Password),
		StatusFlag: "NEED_RESET_PASSWORD",
	}

	if err := a.userRepo.Add(ctx, req.Username, user); err != nil {
		return dto.UserDTO{}, err
	}

	a.userCache.Set(user.ID, user.ToDTO())

	return dto.UserDTO{
		Username:   req.Username,
		IsAdmin:    null.BoolFrom(req.IsAdmin),
		Email:      null.StringFrom(req.Email),
		Fullname:   req.Fullname,
		StatusFlag: null.StringFrom("NEED_RESET_PASSWORD"),
	}, nil
}

func (a *AuthAPI) ChangePasswordEndpoint(ctx context.Context, req dto.PostUserChangePasswordRequestDTO) (dto.AuthResponseDTO, error) {
	auth, err := utils.CheckAuthFromContext(ctx, false)
	if err != nil {
		return dto.AuthResponseDTO{}, err
	}

	if !auth.IsAdmin && auth.Username != req.Username {
		return dto.AuthResponseDTO{}, errs.ErrUnauthorized
	}

	var user User
	err = a.userRepo.Get(ctx, auth.Username, &user)
	if err != nil && !errors.Is(err, errs.ErrKeynotFound) {
		return dto.AuthResponseDTO{}, err
	}

	if errors.Is(err, errs.ErrKeynotFound) {
		return dto.AuthResponseDTO{}, errs.ErrInvalidUserPassword
	}

	user.Password = utils.PasswordHash(req.NewPassword)
	user.StatusFlag = "ACTIVE"

	if err := a.userRepo.Update(ctx, user.ID, &user); err != nil {
		return dto.AuthResponseDTO{}, err
	}

	expiryDuration := 24 * time.Hour
	expiry := time.Now().Add(expiryDuration).UnixMilli()

	token, err := a.createToken(user)
	if err != nil {
		return dto.AuthResponseDTO{}, err
	}

	return dto.AuthResponseDTO{
		Token:      token,
		Fullname:   user.Fullname,
		Username:   user.ID,
		Email:      user.Email,
		IsAdmin:    user.IsAdmin,
		StatusFlag: "ACTIVE",
		Expiry:     expiry,
	}, nil
}

func (a *AuthAPI) ResetUserPasswordEndpoint(ctx context.Context, req dto.PostUserChangePasswordRequestDTO) (any, error) {
	_, err := utils.CheckAuthFromContext(ctx, true)
	if err != nil {
		return dto.AuthResponseDTO{}, err
	}

	var user User
	err = a.userRepo.Get(ctx, req.Username, &user)
	if err != nil && !errors.Is(err, errs.ErrKeynotFound) {
		return dto.AuthResponseDTO{}, err
	}

	if errors.Is(err, errs.ErrKeynotFound) {
		return dto.AuthResponseDTO{}, fmt.Errorf("user not found")
	}

	user.Password = utils.PasswordHash(req.NewPassword)
	user.StatusFlag = "NEED_RESET_PASSWORD"

	if err := a.userRepo.Update(ctx, user.ID, &user); err != nil {
		return dto.AuthResponseDTO{}, err
	}

	return nil, nil
}

func (a *AuthAPI) createToken(user User) (string, error) {
	expiryDuration := 24 * time.Hour

	token, err := utils.CreateToken("bpcs", user.ID, user.Fullname, user.Email, user.StatusFlag, user.IsAdmin, user.Groups, []string{"ssp"}, expiryDuration)
	if err != nil {
		return "", err
	}

	return token, nil
}

func (a *AuthAPI) initUserCache(ctx context.Context) (*utils.SimpleCache[string, dto.UserDTO], error) {
	users, err := a.userRepo.GetAll(ctx)
	if err != nil {
		return nil, err
	}

	userCache := utils.NewCache[string, dto.UserDTO](utils.WithGetterFn(func(key string) (dto.UserDTO, error) {
		var user User
		if err := a.userRepo.Get(ctx, key, &user); err != nil {
			return dto.UserDTO{}, err
		}

		return user.ToDTO(), nil
	}))

	for _, user := range users {
		userCache.Set(user.ID, user.ToDTO())
	}

	return userCache, nil
}

func (a *AuthAPI) validatePostUserRequest(ctx context.Context, req dto.PostUserRequestDTO) error {
	if req.Username == "" {
		return fmt.Errorf("username is required")
	}

	if req.Password == "" {
		return fmt.Errorf("password is required")
	}

	return nil
}
