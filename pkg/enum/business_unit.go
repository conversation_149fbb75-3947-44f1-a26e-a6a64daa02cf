package enum

import (
	"fmt"
	"strconv"
	"strings"
)

type BusinessUnit int

const (
	Core BusinessUnit = iota
	Digital
	Fintech
)

var buStrings = []string{
	"Core",
	"Digital",
	"Fintech",
}

func (b BusinessUnit) String() string {
	if b < 0 || int(b) > len(buStrings)-1 {
		return "Unknown"
	}

	return buStrings[b]
}

func (b *BusinessUnit) UnmarshalText(text []byte) error {
	// Try parsing as integer first
	if val, err := strconv.Atoi(string(text)); err == nil {
		if val >= 0 && val < len(buStrings) {
			*b = BusinessUnit(val)
			return nil
		}
		return fmt.Errorf("invalid BusinessUnit value: %d", val)
	}

	// Try string matching with case insensitivity
	textStr := strings.ToLower(string(text))
	for i, v := range buStrings {
		if strings.ToLower(v) == textStr {
			*b = BusinessUnit(i)
			return nil
		}
	}

	return fmt.<PERSON>rrorf("invalid BusinessUnit value: %s", string(text))
}

func (b *BusinessUnit) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// Remove quotes if present
	dataStr := strings.Trim(string(data), "\"")
	return b.UnmarshalText([]byte(dataStr))
}

func (b BusinessUnit) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%s\"", b.String())), nil
}

func (b *BusinessUnit) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	// Scan a value from the database
	switch v := value.(type) {
	case int:
		strVal := strconv.Itoa(v)
		return b.UnmarshalText([]byte(strVal))
	case int64:
		strVal := strconv.FormatInt(v, 10)
		return b.UnmarshalText([]byte(strVal))
	case []byte:
		return b.UnmarshalText(v)
	case string:
		return b.UnmarshalText([]byte(v))
	}

	return fmt.Errorf("unsupported scan type for BusinessUnit: %T", value)
}
