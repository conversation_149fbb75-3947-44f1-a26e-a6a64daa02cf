package enum

import (
	"fmt"
	"strconv"
	"strings"
)

type Entity int

const (
	IOH Entity = iota
	IM3
	ThreeID
)

var entityStrings = []string{
	"IOH",
	"IM3",
	"3ID",
}

func (e Entity) String() string {
	if e < 0 || int(e) > len(entityStrings)-1 {
		return "Unknown"
	}

	return entityStrings[e]
}

func (e *Entity) UnmarshalText(text []byte) error {
	// Try parsing as integer first
	if val, err := strconv.Atoi(string(text)); err == nil {
		if val >= 0 && val < len(entityStrings) {
			*e = Entity(val)
			return nil
		}
		return fmt.Errorf("invalid Entity value: %d", val)
	}

	// Try string matching with case insensitivity
	for i, v := range entityStrings {
		if v == string(text) {
			*e = Entity(i)
			return nil
		}
	}

	return fmt.Errorf("invalid Entity value: %s", string(text))
}

func (e *Entity) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// Remove quotes if present
	dataStr := strings.Trim(string(data), "\"")
	return e.UnmarshalText([]byte(dataStr))
}

func (e Entity) MarshalJSON() ([]byte, error) {
	return []byte(`"` + e.String() + `"`), nil
}

func (e *Entity) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case int:
		strVal := strconv.Itoa(v)
		return e.UnmarshalText([]byte(strVal))
	case int64:
		strVal := strconv.FormatInt(v, 10)
		return e.UnmarshalText([]byte(strVal))
	case []byte:
		return e.UnmarshalText(v)
	case string:
		return e.UnmarshalText([]byte(v))
	}
	return fmt.Errorf("unsupported scan type for Entity: %T", value)
}
