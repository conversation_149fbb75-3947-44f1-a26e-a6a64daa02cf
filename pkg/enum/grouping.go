package enum

import (
	"fmt"
	"strconv"
	"strings"
)

type Grouping int

const (
	National Grouping = iota
	Circle
	Region
)

var groupingStrings = []string{
	"National",
	"Circle",
	"Region",
}

func (g Grouping) String() string {
	if g < 0 && int(g) > len(groupingStrings)-1 {
		return "Unknown"
	}

	return groupingStrings[g]
}

func (g *Grouping) UnmarshalText(text []byte) error {
	// Try parsing as integer first
	if val, err := strconv.Atoi(string(text)); err == nil {
		if val >= 0 && val < len(groupingStrings) {
			*g = Grouping(val)
			return nil
		}
		return fmt.Errorf("invalid Grouping value: %d", val)
	}

	// Try string matching with case insensitivity
	textStr := strings.ToLower(string(text))
	for i, v := range groupingStrings {
		if strings.ToLower(v) == textStr {
			*g = Grouping(i)
			return nil
		}
	}

	return fmt.<PERSON><PERSON><PERSON>("invalid Grouping value: %s", string(text))
}

func (g *Grouping) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// Remove quotes if present
	dataStr := strings.Trim(string(data), "\"")
	return g.UnmarshalText([]byte(dataStr))
}

func (g Grouping) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%s\"", g.String())), nil
}

func (g *Grouping) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch val := value.(type) {
	case int:
		strVal := strconv.Itoa(val)
		return g.UnmarshalText([]byte(strVal))
	case int64:
		strVal := strconv.FormatInt(val, 10)
		return g.UnmarshalText([]byte(strVal))
	case string:
		return g.UnmarshalText([]byte(val))
	case []byte:
		return g.UnmarshalText(val)
	}
	return fmt.Errorf("unsupported type for Grouping: %T", value)

}
