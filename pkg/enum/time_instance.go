package enum

import (
	"fmt"
	"strconv"
	"strings"
)

type TimeInstance int

const (
	Daily TimeInstance = iota
	FullMonth
	MTD
	YTD
)

var timeInstanceStrings = []string{
	"Daily",
	"FullMonth",
	"MTD",
	"YTD",
}

func (t TimeInstance) String() string {
	if t < Daily || t > YTD {
		return "Unknown"
	}

	return timeInstanceStrings[t]
}

func (t *TimeInstance) UnmarshalText(text []byte) error {
	// Try parsing as integer first
	if val, err := strconv.Atoi(string(text)); err == nil {
		if val >= 0 && val < len(timeInstanceStrings) {
			*t = TimeInstance(val)
			return nil
		}
		return fmt.Errorf("invalid TimeInstance value: %d", val)
	}

	// Try string matching with case insensitivity
	textStr := strings.ToLower(string(text))
	for i, v := range timeInstanceStrings {
		if strings.ToLower(v) == textStr {
			*t = TimeInstance(i)
			return nil
		}
	}

	return fmt.Errorf("invalid TimeInstance value: %s", string(text))
}

func (t *TimeInstance) UnmarshalJSON(data []byte) error {
	if string(data) == "null" {
		return nil
	}

	// Remove quotes if present
	s := string(data)
	if len(s) >= 2 && s[0] == '"' && s[len(s)-1] == '"' {
		s = s[1 : len(s)-1]
	}

	return t.UnmarshalText([]byte(s))
}

func (t TimeInstance) MarshalJSON() ([]byte, error) {
	return []byte(fmt.Sprintf("\"%s\"", t.String())), nil
}

func (t *TimeInstance) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	switch v := value.(type) {
	case int:
		strVal := strconv.Itoa(v)
		return t.UnmarshalText([]byte(strVal))
	case int64:
		strVal := strconv.FormatInt(v, 10)
		return t.UnmarshalText([]byte(strVal))
	case []byte:
		return t.UnmarshalText(v)
	case string:
		return t.UnmarshalText([]byte(v))
	}

	return fmt.Errorf("unsupported type for TimeInstance: %T", value)
}
