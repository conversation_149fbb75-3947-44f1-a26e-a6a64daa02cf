package dto

type Site struct {
	ID            string  `json:"id"`
	Name          string  `json:"name"`
	Longitude     float64 `json:"longitude"`
	Latitude      float64 `json:"latitude"`
	Circle        string  `json:"circle"`
	Region        string  `json:"region"`
	Kabupaten     string  `json:"kabupaten"`
	Kecamatan     string  `json:"kecamatan"`
	IsAddressable bool    `json:"is_addressable"`
	Category      string  `json:"category"`
}
