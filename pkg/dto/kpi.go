package dto

import (
	"github.com/csee-pm/dashboard-pm/pkg/enum"
	"gopkg.in/guregu/null.v4"
)

type Identifier struct {
	Grouping enum.Grouping `json:"grouping"`
	Entity   enum.Entity   `json:"entity"`
	Circle   *string       `json:"circle,omitempty"`
	Region   *string       `json:"region,omitempty"`
}

type TimeInstance struct {
	TiType enum.TimeInstance `json:"type"`
	Name   string            `json:"name"`
	Value  int64             `json:"value"`
}

type KPI[T any] struct {
	TimeInstance TimeInstance `json:"time_instance"`
	Identifier   Identifier   `json:"identifier"`
	KPI          T            `json:"kpi"`
}

type KPIUnit struct {
	Identifier   Identifier   `json:"identifier"`
	TimeInstance TimeInstance `json:"time_instance"`
	Name         string       `json:"name"`
	Value        float64      `json:"value"`
	Desc         null.String  `json:"desc"`
	Unit         string       `json:"unit"`
}
