package dto

import (
	utype "github.com/likearthian/types"
	"gopkg.in/guregu/null.v4"
)

// AuthRequestDTO is a model that used by client when POST from /api/login url
type AuthRequestDTO struct {
	Username string `json:"username"`
	Password string `json:"password"`
}

// AuthResponseDTO is a model that used by client when POST from /api/login url
type AuthResponseDTO struct {
	Token      string   `json:"token"`
	Fullname   string   `json:"fullname"`
	Username   string   `json:"username"`
	Email      string   `json:"email"`
	IsAdmin    bool     `json:"is_admin"`
	StatusFlag string   `json:"status_flag"`
	Groups     []string `json:"groups"`
	Expiry     int64    `json:"expiry"`
}

type GetUserRequestDTO struct {
	Username     null.String   `query:"username"`
	Subordinates utype.Boolean `query:"subordinate"`
}

// PostUserRequestDTO is a model that used by client when POST from /api/user url
type PostUserRequestDTO struct {
	Username string   `json:"username"`
	Password string   `json:"password"`
	IsA<PERSON><PERSON>  bool     `json:"is_admin"`
	Email    string   `json:"email"`
	Fullname string   `json:"fullname"`
	Groups   []string `json:"groups"`
}

// UserDTO represents the data transfer object for a user.
type UserDTO struct {
	Username   string      `json:"username"`
	IsAdmin    null.Bool   `json:"is_admin,omitempty"`
	Email      null.String `json:"email,omitempty"`
	Fullname   string      `json:"fullname"`
	StatusFlag null.String `json:"status_flag,omitempty"`
	ReportTo   null.String `json:"report_to,omitempty"`
	Groups     []string    `json:"groups,omitempty"`
}

// PostUserChangePasswordRequestDTO is a model that used by client when POST from
// /api/idm/password
// and /api/idm/reset-password url
type PostUserChangePasswordRequestDTO struct {
	Username    string `json:"username"`
	NewPassword string `json:"new_password"`
}

type GroupDTO struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}
