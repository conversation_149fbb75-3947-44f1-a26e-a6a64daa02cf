package dto

import (
	"github.com/csee-pm/dashboard-pm/pkg/enum"
)

type GetReportRequestDTO struct {
	ReportID  string            `query:"report_id"`
	Entity    enum.Entity       `query:"entity"`
	Grouping  enum.Grouping     `query:"grouping_id"`
	TiType    enum.TimeInstance `query:"t_instance_id"`
	StartTime string            `query:"start_time"`
	EndTime   string            `query:"end_time"`
	BUType    enum.BusinessUnit `query:"bu_id"`
}

type ReportBase struct {
	Entity    string `json:"entity"`
	Level     string `json:"level"`
	TiType    string `json:"ti_type"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
}
