package dto

import (
	"fmt"
	"net/http"

	"github.com/csee-pm/dashboard-pm/pkg/errs"
)

type BaseResponse[T any] struct {
	RequestID  string         `json:"request_id"`
	Code       int            `json:"code"`
	Message    string         `json:"message"`
	Data       T              `json:"data"`
	Pagination *PaginationDTO `json:"pagination,omitempty"`
}

type ListItem struct {
	Value string `json:"value"`
	Label string `json:"label"`
	Count *int   `json:"count,omitempty"`
}

type PaginationDTO struct {
	Page  uint `json:"page"`
	Total uint `json:"total"`
}

type PagedData[T any] struct {
	Data       T
	Pagination PaginationDTO
}

type ByIDRequestDTO[T comparable] struct {
	ID T `query:"id" json:"id"`
}

func SuccessResponse[T any](requestID string, data T, pagination ...PaginationDTO) BaseResponse[T] {
	respon := BaseResponse[T]{
		RequestID: requestID,
		Code:      200,
		Message:   "success",
		Data:      data,
	}

	if len(pagination) > 0 {
		respon.Pagination = &pagination[0]
	}
	return respon
}

func ErrorResponse(requestID string, err error) BaseResponse[any] {
	code := errs.Err2Code(err)

	return BaseResponse[any]{
		RequestID: requestID,
		Code:      code,
		Message:   fmt.Sprintf("%s. %s", http.StatusText(code), err.Error()),
	}
}
