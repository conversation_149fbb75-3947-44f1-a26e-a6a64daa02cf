package api

import (
	"context"
	"fmt"

	"cloud.google.com/go/bigquery"
	"cloud.google.com/go/firestore"
	"github.com/csee-pm/dashboard-pm/internal/auth"
	"github.com/csee-pm/dashboard-pm/internal/config"
	"github.com/csee-pm/dashboard-pm/internal/report"
	"github.com/likearthian/apikit/logger"
	"github.com/likearthian/go-crypto"
	"google.golang.org/api/option"
)

type APIEndpoints struct {
	Auth   *auth.AuthAPI
	Report *report.ReportAPI
}

func CreateAPIEndpoints(conf *config.Config, logger logger.Logger) (APIEndpoints, error) {
	gcloud := conf.GetGCloudConfig()
	bqconf := conf.GetBQConfig()
	fsconf := conf.GetFirestoreConfig()

	var db *bigquery.Client
	var err error

	if bqconf.ProjectID != "" {
		var bqKey []byte
		bqKey, err = crypto.DecodeBASE64(bqconf.JsonKey)
		if err != nil {
			return APIEndpoints{}, fmt.Errorf("failed to decode BQ json key: %w", err)
		}

		db, err = bigquery.NewClient(context.Background(), bqconf.ProjectID, option.WithCredentialsJSON(bqKey))
	} else {
		db, err = bigquery.NewClient(context.Background(), gcloud.ProjectID)
	}

	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to create bigquery client: %w, project: %s", err, gcloud.ProjectID)
	}

	fstoreKey, err := crypto.DecodeBASE64(fsconf.JsonKey)
	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to decode json key: %w", err)
	}

	fsdb, err := firestore.NewClient(context.Background(), fsconf.ProjectID, option.WithCredentialsJSON(fstoreKey))
	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to create firestore client: %w, project: %s", err, fsconf.ProjectID)
	}

	reportApi := report.CreateReportAPI(db)
	authApi, err := auth.CreateAuthAPI(fsdb, conf)
	if err != nil {
		return APIEndpoints{}, fmt.Errorf("failed to create auth api: %w", err)
	}

	return APIEndpoints{Report: &reportApi, Auth: authApi}, nil
}
