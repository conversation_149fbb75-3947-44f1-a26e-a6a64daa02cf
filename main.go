package main

import (
	"context"
	"embed"
	"fmt"
	"io"
	"io/fs"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"github.com/csee-pm/dashboard-pm/api"
	htgw "github.com/csee-pm/dashboard-pm/gateway/http"
	"github.com/csee-pm/dashboard-pm/internal/config"
	"github.com/joho/godotenv"
	log "github.com/likearthian/apikit/logger"
	"github.com/sirupsen/logrus"
	"golang.ngrok.com/ngrok"
	ngconfig "golang.ngrok.com/ngrok/config"
)

//go:embed all:frontend/build
var frontend embed.FS

func main() {
	output := os.Stdout
	err := godotenv.Load()
	if err != nil {
		fmt.Println("Error loading .env file")
	}

	conf := config.CreateConfig()
	logger := createLogger(log.Level(conf.GetServerConfig().LogLevel), output)

	endpoints, err := api.CreateAPIEndpoints(conf, logger)
	if err != nil {
		panic(err)
	}

	errChan := make(chan error)
	go func() {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		errChan <- fmt.Errorf("%s", <-c)
	}()

	// Frontend App
	feApp, err := fs.Sub(frontend, "frontend/build")
	if err != nil {
		panic(err)
	}

	useNgrok := false
	if os.Getenv("NGROK_AUTHTOKEN") != "" {
		useNgrok = true
	}

	httpAddr := fmt.Sprintf(":%d", conf.GetServerConfig().Port)
	httpServer := htgw.NewHTTPServer(endpoints, feApp, logger)
	go func() {
		logger.Info(fmt.Sprintf("dashboard-pm server is running on %s", httpAddr))
		if useNgrok {
			errChan <- run_ngrok(httpServer, logger)
			return
		}
		errChan <- http.ListenAndServe(httpAddr, httpServer)
	}()

	logger.Info("terminated", <-errChan)

}

func run_ngrok(handler http.Handler, logger log.Logger) error {
	listener, err := ngrok.Listen(context.Background(),
		ngconfig.HTTPEndpoint(),
		ngrok.WithAuthtokenFromEnv(),
	)

	if err != nil {
		return err
	}

	logger.Info("Ingress established", "address", listener.URL())

	return http.Serve(listener, handler)
}

func createLogger(level log.Level, out io.Writer) log.Logger {
	ruslog := logrus.New()
	ruslog.SetFormatter(&logrus.TextFormatter{FullTimestamp: true})
	ruslog.SetOutput(out)

	logger := log.NewRusLog(ruslog)
	logger.SetLevel(level)

	return logger
}
